import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { PreviewService } from './preview.service';
import { PreviewUpdateEvent } from './preview.interface';

@WebSocketGateway({
  namespace: '/preview',
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
})
export class PreviewGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(PreviewGateway.name);
  private readonly clientSessionMap = new Map<string, string>(); // socketId -> sessionId

  constructor(private readonly previewService: PreviewService) {}

  afterInit(server: Server) {
    this.logger.log('预览WebSocket网关已初始化');
  }

  handleConnection(client: Socket) {
    this.logger.log(`预览客户端连接: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`预览客户端断开连接: ${client.id}`);
    this.clientSessionMap.delete(client.id);
  }

  /**
   * 订阅预览会话
   */
  @SubscribeMessage('subscribe-session')
  async handleSubscribeSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string }
  ) {
    try {
      const { sessionId } = data;
      
      // 验证会话是否存在
      const session = await this.previewService.getPreviewSession(sessionId);
      
      // 加入会话房间
      client.join(`session:${sessionId}`);
      this.clientSessionMap.set(client.id, sessionId);
      
      // 发送当前会话状态
      client.emit('session-status', {
        sessionId,
        status: session.status,
        config: session.config,
        timestamp: new Date()
      });
      
      this.logger.log(`客户端 ${client.id} 订阅预览会话 ${sessionId}`);
    } catch (error) {
      this.logger.error(`订阅预览会话失败: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 取消订阅预览会话
   */
  @SubscribeMessage('unsubscribe-session')
  handleUnsubscribeSession(@ConnectedSocket() client: Socket) {
    const sessionId = this.clientSessionMap.get(client.id);
    if (sessionId) {
      client.leave(`session:${sessionId}`);
      this.clientSessionMap.delete(client.id);
      this.logger.log(`客户端 ${client.id} 取消订阅预览会话 ${sessionId}`);
    }
  }

  /**
   * 更新预览配置
   */
  @SubscribeMessage('update-config')
  async handleUpdateConfig(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string; config: any }
  ) {
    try {
      const { sessionId, config } = data;
      
      // 更新预览配置
      const session = await this.previewService.updatePreviewConfig(sessionId, config);
      
      // 广播配置更新到所有订阅者
      this.server.to(`session:${sessionId}`).emit('config-updated', {
        sessionId,
        config: session.config,
        timestamp: new Date()
      });
      
      this.logger.log(`预览配置已更新: ${sessionId}`);
    } catch (error) {
      this.logger.error(`更新预览配置失败: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 请求刷新预览
   */
  @SubscribeMessage('refresh-preview')
  async handleRefreshPreview(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string }
  ) {
    try {
      const { sessionId } = data;
      
      // 触发预览刷新
      this.server.to(`session:${sessionId}`).emit('preview-refresh', {
        sessionId,
        timestamp: new Date()
      });
      
      this.logger.log(`预览刷新请求: ${sessionId}`);
    } catch (error) {
      this.logger.error(`刷新预览失败: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 发送预览交互事件
   */
  @SubscribeMessage('preview-interaction')
  handlePreviewInteraction(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: {
      sessionId: string;
      type: string;
      target: string;
      data: any;
    }
  ) {
    const { sessionId, type, target, data: interactionData } = data;
    
    // 广播交互事件到其他客户端
    client.to(`session:${sessionId}`).emit('interaction-event', {
      sessionId,
      type,
      target,
      data: interactionData,
      timestamp: new Date()
    });
    
    this.logger.log(`预览交互事件: ${sessionId} - ${type}`);
  }

  /**
   * 心跳检测
   */
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', { timestamp: Date.now() });
  }

  // 事件监听器

  /**
   * 监听预览更新事件
   */
  @OnEvent('preview.updated')
  handlePreviewUpdated(event: PreviewUpdateEvent) {
    this.server.to(`session:${event.sessionId}`).emit('preview-updated', {
      sessionId: event.sessionId,
      type: event.type,
      data: event.data,
      timestamp: event.timestamp
    });
  }

  /**
   * 监听预览状态变化
   */
  @OnEvent('preview.status-changed')
  handlePreviewStatusChanged(event: { sessionId: string; status: string; data?: any }) {
    this.server.to(`session:${event.sessionId}`).emit('status-changed', {
      sessionId: event.sessionId,
      status: event.status,
      data: event.data,
      timestamp: new Date()
    });
  }

  /**
   * 监听预览错误
   */
  @OnEvent('preview.error')
  handlePreviewError(event: { sessionId: string; error: string; details?: any }) {
    this.server.to(`session:${event.sessionId}`).emit('preview-error', {
      sessionId: event.sessionId,
      error: event.error,
      details: event.details,
      timestamp: new Date()
    });
  }

  /**
   * 向特定会话发送消息
   */
  sendToSession(sessionId: string, event: string, data: any) {
    this.server.to(`session:${sessionId}`).emit(event, data);
  }

  /**
   * 向所有客户端广播消息
   */
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }

  /**
   * 获取会话的连接客户端数量
   */
  getSessionClientCount(sessionId: string): number {
    const room = this.server.sockets.adapter.rooms.get(`session:${sessionId}`);
    return room ? room.size : 0;
  }

  /**
   * 获取活跃会话列表
   */
  getActiveSessions(): string[] {
    const sessions = new Set<string>();
    for (const sessionId of this.clientSessionMap.values()) {
      sessions.add(sessionId);
    }
    return Array.from(sessions);
  }
}
