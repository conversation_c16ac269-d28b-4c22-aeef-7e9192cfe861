/**
 * A/B测试服务
 * 
 * 提供A/B测试的创建、管理和分析功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Redis from 'ioredis';
import { ABTestConfig, ABTestVariant } from './distributed-behavior.service';

/**
 * A/B测试参与记录
 */
export interface TestParticipation {
  testId: string;
  variantId: string;
  entityId: string;
  sessionId: string;
  timestamp: number;
  metadata?: any;
}

/**
 * A/B测试事件
 */
export interface TestEvent {
  testId: string;
  variantId: string;
  entityId: string;
  sessionId: string;
  eventType: string;
  eventData: any;
  timestamp: number;
}

/**
 * A/B测试统计结果
 */
export interface TestStatistics {
  testId: string;
  totalParticipants: number;
  variants: VariantStatistics[];
  startTime: number;
  endTime?: number;
  isSignificant: boolean;
  confidenceLevel: number;
  pValue?: number;
}

/**
 * 变体统计
 */
export interface VariantStatistics {
  variantId: string;
  participants: number;
  conversions: number;
  conversionRate: number;
  averageValue: number;
  standardDeviation: number;
  confidenceInterval: [number, number];
}

/**
 * A/B测试服务
 */
@Injectable()
export class ABTestService {
  private readonly logger = new Logger(ABTestService.name);
  
  private activeTests = new Map<string, ABTestConfig>();
  private testAssignments = new Map<string, Map<string, string>>(); // entityId -> testId -> variantId
  
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly redis: Redis
  ) {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 加载活跃的A/B测试
      await this.loadActiveTests();
      
      // 启动定期统计分析
      this.startPeriodicAnalysis();
      
      this.logger.log('A/B测试服务已初始化');
      
    } catch (error) {
      this.logger.error('A/B测试服务初始化失败:', error);
    }
  }

  /**
   * 加载活跃测试
   */
  private async loadActiveTests(): Promise<void> {
    const testConfigs = await this.redis.hgetall('behavior:ab_tests');
    const now = Date.now();
    
    for (const [testId, configData] of Object.entries(testConfigs)) {
      try {
        const config = JSON.parse(configData) as ABTestConfig;
        
        // 检查测试是否在活跃期间
        if (config.isActive && config.startTime <= now && config.endTime > now) {
          this.activeTests.set(testId, config);
        }
      } catch (error) {
        this.logger.error(`加载A/B测试配置失败 [${testId}]:`, error);
      }
    }
    
    this.logger.log(`加载了 ${this.activeTests.size} 个活跃的A/B测试`);
  }

  /**
   * 创建A/B测试
   */
  public async createTest(config: ABTestConfig): Promise<void> {
    // 验证配置
    this.validateTestConfig(config);
    
    // 保存配置
    await this.redis.hset(
      'behavior:ab_tests',
      config.testId,
      JSON.stringify(config)
    );
    
    // 如果测试是活跃的，添加到内存中
    if (config.isActive) {
      this.activeTests.set(config.testId, config);
    }
    
    // 初始化测试数据结构
    await this.initializeTestData(config);
    
    this.logger.log(`A/B测试已创建: ${config.testId}`);
    
    // 发布事件
    this.eventEmitter.emit('ab_test.created', { config });
  }

  /**
   * 获取用户的测试变体
   */
  public async getVariantForUser(
    testId: string,
    entityId: string,
    sessionId: string,
    context?: any
  ): Promise<ABTestVariant | null> {
    const test = this.activeTests.get(testId);
    if (!test) {
      return null;
    }
    
    // 检查用户是否已经分配了变体
    const existingAssignment = await this.getExistingAssignment(testId, entityId);
    if (existingAssignment) {
      return test.variants.find(v => v.id === existingAssignment) || null;
    }
    
    // 检查用户是否符合测试条件
    if (!this.isUserEligible(test, entityId, context)) {
      return null;
    }
    
    // 分配新变体
    const variant = this.assignVariant(test, entityId);
    
    // 记录分配
    await this.recordAssignment(testId, entityId, variant.id);
    
    // 记录参与
    await this.recordParticipation({
      testId,
      variantId: variant.id,
      entityId,
      sessionId,
      timestamp: Date.now(),
      metadata: context
    });
    
    return variant;
  }

  /**
   * 记录测试事件
   */
  public async recordEvent(event: TestEvent): Promise<void> {
    // 验证事件
    if (!this.activeTests.has(event.testId)) {
      this.logger.warn(`尝试记录不存在的测试事件: ${event.testId}`);
      return;
    }
    
    // 保存事件
    await this.redis.lpush(
      `ab_test:${event.testId}:events`,
      JSON.stringify(event)
    );
    
    // 更新实时统计
    await this.updateRealTimeStats(event);
    
    this.logger.debug(`记录测试事件: ${event.testId}/${event.variantId}/${event.eventType}`);
  }

  /**
   * 停止测试
   */
  public async stopTest(testId: string): Promise<void> {
    const test = this.activeTests.get(testId);
    if (!test) {
      throw new Error(`测试不存在: ${testId}`);
    }
    
    // 更新配置
    test.isActive = false;
    test.endTime = Date.now();
    
    // 保存到Redis
    await this.redis.hset(
      'behavior:ab_tests',
      testId,
      JSON.stringify(test)
    );
    
    // 从活跃测试中移除
    this.activeTests.delete(testId);
    
    // 生成最终报告
    const finalStats = await this.generateTestStatistics(testId);
    
    this.logger.log(`A/B测试已停止: ${testId}`);
    
    // 发布事件
    this.eventEmitter.emit('ab_test.stopped', { testId, finalStats });
  }

  /**
   * 获取测试统计
   */
  public async getTestStatistics(testId: string): Promise<TestStatistics> {
    return await this.generateTestStatistics(testId);
  }

  /**
   * 获取所有活跃测试
   */
  public getActiveTests(): ABTestConfig[] {
    return Array.from(this.activeTests.values());
  }

  /**
   * 验证测试配置
   */
  private validateTestConfig(config: ABTestConfig): void {
    if (!config.testId || !config.name) {
      throw new Error('测试ID和名称是必需的');
    }
    
    if (!config.variants || config.variants.length < 2) {
      throw new Error('至少需要2个变体');
    }
    
    if (config.trafficSplit.length !== config.variants.length) {
      throw new Error('流量分配数量必须与变体数量匹配');
    }
    
    const totalSplit = config.trafficSplit.reduce((sum, split) => sum + split, 0);
    if (Math.abs(totalSplit - 100) > 0.01) {
      throw new Error('流量分配总和必须为100%');
    }
  }

  /**
   * 初始化测试数据
   */
  private async initializeTestData(config: ABTestConfig): Promise<void> {
    // 创建测试相关的Redis键
    const keys = [
      `ab_test:${config.testId}:participations`,
      `ab_test:${config.testId}:events`,
      `ab_test:${config.testId}:assignments`,
      `ab_test:${config.testId}:stats`
    ];
    
    // 初始化统计数据
    const initialStats = {
      testId: config.testId,
      totalParticipants: 0,
      variants: config.variants.map(variant => ({
        variantId: variant.id,
        participants: 0,
        conversions: 0,
        conversionRate: 0,
        averageValue: 0,
        standardDeviation: 0,
        confidenceInterval: [0, 0] as [number, number]
      })),
      startTime: config.startTime,
      isSignificant: false,
      confidenceLevel: 0.95
    };
    
    await this.redis.set(
      `ab_test:${config.testId}:stats`,
      JSON.stringify(initialStats)
    );
  }

  /**
   * 获取现有分配
   */
  private async getExistingAssignment(testId: string, entityId: string): Promise<string | null> {
    return await this.redis.hget(`ab_test:${testId}:assignments`, entityId);
  }

  /**
   * 检查用户资格
   */
  private isUserEligible(test: ABTestConfig, entityId: string, context?: any): boolean {
    // 检查分段规则
    if (test.segmentationRules) {
      // 这里应该实现具体的分段逻辑
      // 暂时返回true
    }
    
    return true;
  }

  /**
   * 分配变体
   */
  private assignVariant(test: ABTestConfig, entityId: string): ABTestVariant {
    // 使用一致性哈希确保同一用户总是得到相同的变体
    const hash = this.hashString(entityId + test.testId);
    const randomValue = (hash % 10000) / 100; // 0-100之间的值
    
    let cumulativeWeight = 0;
    for (let i = 0; i < test.variants.length; i++) {
      cumulativeWeight += test.trafficSplit[i];
      if (randomValue <= cumulativeWeight) {
        return test.variants[i];
      }
    }
    
    // 默认返回第一个变体
    return test.variants[0];
  }

  /**
   * 记录分配
   */
  private async recordAssignment(testId: string, entityId: string, variantId: string): Promise<void> {
    await this.redis.hset(`ab_test:${testId}:assignments`, entityId, variantId);
  }

  /**
   * 记录参与
   */
  private async recordParticipation(participation: TestParticipation): Promise<void> {
    await this.redis.lpush(
      `ab_test:${participation.testId}:participations`,
      JSON.stringify(participation)
    );
  }

  /**
   * 更新实时统计
   */
  private async updateRealTimeStats(event: TestEvent): Promise<void> {
    const statsKey = `ab_test:${event.testId}:stats`;
    const statsData = await this.redis.get(statsKey);
    
    if (statsData) {
      const stats = JSON.parse(statsData) as TestStatistics;
      
      // 更新变体统计
      const variantStats = stats.variants.find(v => v.variantId === event.variantId);
      if (variantStats) {
        if (event.eventType === 'conversion') {
          variantStats.conversions++;
          variantStats.conversionRate = variantStats.conversions / variantStats.participants;
        }
      }
      
      // 保存更新的统计
      await this.redis.set(statsKey, JSON.stringify(stats));
    }
  }

  /**
   * 生成测试统计
   */
  private async generateTestStatistics(testId: string): Promise<TestStatistics> {
    // 获取参与数据
    const participations = await this.redis.lrange(`ab_test:${testId}:participations`, 0, -1);
    const events = await this.redis.lrange(`ab_test:${testId}:events`, 0, -1);
    
    // 分析数据
    const variantStats = new Map<string, VariantStatistics>();
    
    // 统计参与者
    for (const participationData of participations) {
      try {
        const participation = JSON.parse(participationData) as TestParticipation;
        
        if (!variantStats.has(participation.variantId)) {
          variantStats.set(participation.variantId, {
            variantId: participation.variantId,
            participants: 0,
            conversions: 0,
            conversionRate: 0,
            averageValue: 0,
            standardDeviation: 0,
            confidenceInterval: [0, 0]
          });
        }
        
        variantStats.get(participation.variantId)!.participants++;
      } catch (error) {
        this.logger.error('解析参与数据失败:', error);
      }
    }
    
    // 统计转化
    for (const eventData of events) {
      try {
        const event = JSON.parse(eventData) as TestEvent;
        
        if (event.eventType === 'conversion') {
          const stats = variantStats.get(event.variantId);
          if (stats) {
            stats.conversions++;
          }
        }
      } catch (error) {
        this.logger.error('解析事件数据失败:', error);
      }
    }
    
    // 计算转化率和置信区间
    for (const stats of variantStats.values()) {
      if (stats.participants > 0) {
        stats.conversionRate = stats.conversions / stats.participants;
        stats.confidenceInterval = this.calculateConfidenceInterval(
          stats.conversions,
          stats.participants,
          0.95
        );
      }
    }
    
    const test = this.activeTests.get(testId);
    const totalParticipants = Array.from(variantStats.values())
      .reduce((sum, stats) => sum + stats.participants, 0);
    
    return {
      testId,
      totalParticipants,
      variants: Array.from(variantStats.values()),
      startTime: test?.startTime || 0,
      endTime: test?.endTime,
      isSignificant: this.calculateSignificance(Array.from(variantStats.values())),
      confidenceLevel: 0.95,
      pValue: this.calculatePValue(Array.from(variantStats.values()))
    };
  }

  /**
   * 启动定期分析
   */
  private startPeriodicAnalysis(): void {
    // 每小时分析一次测试结果
    setInterval(() => {
      this.analyzeAllActiveTests();
    }, 3600000);
  }

  /**
   * 分析所有活跃测试
   */
  private async analyzeAllActiveTests(): Promise<void> {
    for (const testId of this.activeTests.keys()) {
      try {
        const stats = await this.generateTestStatistics(testId);
        
        // 检查是否达到统计显著性
        if (stats.isSignificant && stats.pValue && stats.pValue < 0.05) {
          this.eventEmitter.emit('ab_test.significant_result', { testId, stats });
        }
        
        // 检查是否需要提前停止
        if (this.shouldEarlyStop(stats)) {
          this.eventEmitter.emit('ab_test.early_stop_recommended', { testId, stats });
        }
        
      } catch (error) {
        this.logger.error(`分析测试失败 [${testId}]:`, error);
      }
    }
  }

  // 辅助方法
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }

  private calculateConfidenceInterval(
    conversions: number,
    participants: number,
    confidence: number
  ): [number, number] {
    if (participants === 0) return [0, 0];
    
    const rate = conversions / participants;
    const z = 1.96; // 95% confidence
    const margin = z * Math.sqrt((rate * (1 - rate)) / participants);
    
    return [Math.max(0, rate - margin), Math.min(1, rate + margin)];
  }

  private calculateSignificance(variants: VariantStatistics[]): boolean {
    // 简化的显著性检验
    if (variants.length < 2) return false;
    
    const [control, treatment] = variants;
    if (!control || !treatment) return false;
    
    return Math.abs(control.conversionRate - treatment.conversionRate) > 0.02;
  }

  private calculatePValue(variants: VariantStatistics[]): number {
    // 简化的p值计算
    return Math.random() * 0.1; // 模拟p值
  }

  private shouldEarlyStop(stats: TestStatistics): boolean {
    // 简化的提前停止逻辑
    return stats.totalParticipants > 1000 && stats.isSignificant;
  }
}
