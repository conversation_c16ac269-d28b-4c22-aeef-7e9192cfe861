/**
 * 分布式行为决策服务
 *
 * 提供大规模分布式环境下的行为决策和协调功能。
 * 支持多实例协调、负载均衡、故障恢复等企业级特性。
 *
 * 新增功能：
 * - 机器学习决策模型
 * - A/B测试框架
 * - 高级决策算法
 * - 智能负载均衡
 */

import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import {
  DecisionContext,
  DecisionOption,
  DecisionResult,
  DecisionStrategy
} from '@engine/ai/behavior/IntelligentDecisionSystem';

/**
 * 分布式决策请求
 */
export interface DistributedDecisionRequest {
  requestId: string;
  entityId: string;
  sessionId: string;
  context: DecisionContext;
  options: DecisionOption[];
  strategy?: string;
  priority: number;
  timeout: number;
  timestamp: number;
}

/**
 * 分布式决策响应
 */
export interface DistributedDecisionResponse {
  requestId: string;
  entityId: string;
  result: DecisionResult;
  processingTime: number;
  nodeId: string;
  timestamp: number;
}

/**
 * 节点状态
 */
export interface NodeStatus {
  nodeId: string;
  isActive: boolean;
  load: number;
  capacity: number;
  lastHeartbeat: number;
  processedRequests: number;
  averageResponseTime: number;
  errorRate: number;
}

/**
 * 协调策略
 */
export enum CoordinationStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_LOAD = 'least_load',
  GEOGRAPHIC = 'geographic',
  CAPABILITY_BASED = 'capability_based',
  ML_OPTIMIZED = 'ml_optimized',
  AB_TEST_AWARE = 'ab_test_aware'
}

/**
 * 机器学习模型类型
 */
export enum MLModelType {
  DECISION_TREE = 'decision_tree',
  RANDOM_FOREST = 'random_forest',
  NEURAL_NETWORK = 'neural_network',
  REINFORCEMENT_LEARNING = 'reinforcement_learning',
  ENSEMBLE = 'ensemble'
}

/**
 * A/B测试配置
 */
export interface ABTestConfig {
  testId: string;
  name: string;
  description: string;
  variants: ABTestVariant[];
  trafficSplit: number[];
  startTime: number;
  endTime: number;
  isActive: boolean;
  targetMetrics: string[];
  segmentationRules?: any;
}

/**
 * A/B测试变体
 */
export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  strategy: string;
  parameters: any;
  weight: number;
}

/**
 * 机器学习模型配置
 */
export interface MLModelConfig {
  modelId: string;
  type: MLModelType;
  name: string;
  version: string;
  parameters: any;
  trainingData?: any;
  accuracy: number;
  lastTrained: number;
  isActive: boolean;
}

/**
 * 决策性能指标
 */
export interface DecisionMetrics {
  accuracy: number;
  responseTime: number;
  userSatisfaction: number;
  conversionRate: number;
  errorRate: number;
  throughput: number;
  timestamp: number;
}

/**
 * 分布式行为决策服务
 */
@Injectable()
export class DistributedBehaviorService {
  private readonly logger = new Logger(DistributedBehaviorService.name);
  private readonly nodeId: string;
  private readonly redis: Redis;
  private readonly pubsub: Redis;
  
  private isActive = false;
  private currentLoad = 0;
  private maxCapacity = 100;
  private processedRequests = 0;
  private totalResponseTime = 0;
  private errorCount = 0;
  
  private pendingRequests = new Map<string, DistributedDecisionRequest>();
  private nodeStatuses = new Map<string, NodeStatus>();
  private coordinationStrategy = CoordinationStrategy.LEAST_LOAD;

  // 决策策略实例
  private decisionStrategies = new Map<string, DecisionStrategy>();

  // 机器学习模型
  private mlModels = new Map<string, MLModelConfig>();
  private activeMLModel: string | null = null;

  // A/B测试
  private abTests = new Map<string, ABTestConfig>();
  private activeABTests = new Set<string>();

  // 性能指标
  private metricsHistory: DecisionMetrics[] = [];
  private currentMetrics: DecisionMetrics = {
    accuracy: 0,
    responseTime: 0,
    userSatisfaction: 0,
    conversionRate: 0,
    errorRate: 0,
    throughput: 0,
    timestamp: Date.now()
  };

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject('REDIS_CONFIG') redisConfig: any
  ) {
    this.nodeId = `behavior-node-${process.env.NODE_ID || Date.now()}`;
    this.redis = new Redis(redisConfig);
    this.pubsub = new Redis(redisConfig);

    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 注册节点
      await this.registerNode();
      
      // 设置消息监听
      await this.setupMessageHandlers();
      
      // 启动心跳
      this.startHeartbeat();
      
      // 初始化决策策略
      this.initializeDecisionStrategies();

      // 初始化机器学习模型
      await this.initializeMLModels();

      // 初始化A/B测试
      await this.initializeABTests();

      // 启动性能监控
      this.startPerformanceMonitoring();

      this.isActive = true;
      this.logger.log(`分布式行为决策服务已启动，节点ID: ${this.nodeId}`);

    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册节点
   */
  private async registerNode(): Promise<void> {
    const nodeInfo: NodeStatus = {
      nodeId: this.nodeId,
      isActive: true,
      load: 0,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: 0,
      averageResponseTime: 0,
      errorRate: 0
    };
    
    await this.redis.hset(
      'behavior:nodes',
      this.nodeId,
      JSON.stringify(nodeInfo)
    );
    
    this.logger.log(`节点已注册: ${this.nodeId}`);
  }

  /**
   * 设置消息处理器
   */
  private async setupMessageHandlers(): Promise<void> {
    // 监听决策请求
    this.pubsub.subscribe('behavior:requests');
    this.pubsub.subscribe(`behavior:requests:${this.nodeId}`);
    
    // 监听协调消息
    this.pubsub.subscribe('behavior:coordination');
    
    this.pubsub.on('message', async (channel, message) => {
      try {
        const data = JSON.parse(message);
        
        switch (channel) {
          case 'behavior:requests':
            await this.handleDecisionRequest(data);
            break;
          case `behavior:requests:${this.nodeId}`:
            await this.handleDirectRequest(data);
            break;
          case 'behavior:coordination':
            await this.handleCoordinationMessage(data);
            break;
        }
      } catch (error) {
        this.logger.error('消息处理失败:', error);
      }
    });
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    setInterval(async () => {
      try {
        await this.sendHeartbeat();
      } catch (error) {
        this.logger.error('心跳发送失败:', error);
      }
    }, 5000); // 5秒心跳间隔
  }

  /**
   * 发送心跳
   */
  private async sendHeartbeat(): Promise<void> {
    const nodeInfo: NodeStatus = {
      nodeId: this.nodeId,
      isActive: this.isActive,
      load: this.currentLoad,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: this.processedRequests,
      averageResponseTime: this.processedRequests > 0 ? this.totalResponseTime / this.processedRequests : 0,
      errorRate: this.processedRequests > 0 ? this.errorCount / this.processedRequests : 0
    };
    
    await this.redis.hset(
      'behavior:nodes',
      this.nodeId,
      JSON.stringify(nodeInfo)
    );
    
    // 发布心跳事件
    await this.redis.publish('behavior:heartbeat', JSON.stringify({
      nodeId: this.nodeId,
      timestamp: Date.now(),
      status: nodeInfo
    }));
  }

  /**
   * 初始化决策策略
   */
  private initializeDecisionStrategies(): void {
    // 这里应该加载各种决策策略
    // 暂时使用模拟实现
    this.logger.log('决策策略已初始化');
  }

  /**
   * 初始化机器学习模型
   */
  private async initializeMLModels(): Promise<void> {
    try {
      // 加载已保存的模型配置
      const modelConfigs = await this.redis.hgetall('behavior:ml_models');

      for (const [modelId, configData] of Object.entries(modelConfigs)) {
        try {
          const config = JSON.parse(configData) as MLModelConfig;
          this.mlModels.set(modelId, config);

          if (config.isActive && !this.activeMLModel) {
            this.activeMLModel = modelId;
          }
        } catch (error) {
          this.logger.error(`加载ML模型配置失败 [${modelId}]:`, error);
        }
      }

      // 如果没有活跃模型，创建默认模型
      if (!this.activeMLModel) {
        await this.createDefaultMLModel();
      }

      this.logger.log(`机器学习模型已初始化，活跃模型: ${this.activeMLModel}`);

    } catch (error) {
      this.logger.error('ML模型初始化失败:', error);
      // 创建默认模型作为后备
      await this.createDefaultMLModel();
    }
  }

  /**
   * 创建默认机器学习模型
   */
  private async createDefaultMLModel(): Promise<void> {
    const defaultModel: MLModelConfig = {
      modelId: 'default_decision_tree',
      type: MLModelType.DECISION_TREE,
      name: '默认决策树模型',
      version: '1.0.0',
      parameters: {
        maxDepth: 10,
        minSamplesLeaf: 5,
        criterion: 'gini'
      },
      accuracy: 0.75,
      lastTrained: Date.now(),
      isActive: true
    };

    this.mlModels.set(defaultModel.modelId, defaultModel);
    this.activeMLModel = defaultModel.modelId;

    // 保存到Redis
    await this.redis.hset(
      'behavior:ml_models',
      defaultModel.modelId,
      JSON.stringify(defaultModel)
    );

    this.logger.log('默认ML模型已创建');
  }

  /**
   * 初始化A/B测试
   */
  private async initializeABTests(): Promise<void> {
    try {
      // 加载已保存的A/B测试配置
      const testConfigs = await this.redis.hgetall('behavior:ab_tests');
      const now = Date.now();

      for (const [testId, configData] of Object.entries(testConfigs)) {
        try {
          const config = JSON.parse(configData) as ABTestConfig;
          this.abTests.set(testId, config);

          // 检查测试是否在活跃期间
          if (config.isActive && config.startTime <= now && config.endTime > now) {
            this.activeABTests.add(testId);
          }
        } catch (error) {
          this.logger.error(`加载A/B测试配置失败 [${testId}]:`, error);
        }
      }

      this.logger.log(`A/B测试已初始化，活跃测试数: ${this.activeABTests.size}`);

    } catch (error) {
      this.logger.error('A/B测试初始化失败:', error);
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    // 每分钟收集一次性能指标
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 60000);

    // 每小时分析性能趋势
    setInterval(() => {
      this.analyzePerformanceTrends();
    }, 3600000);

    this.logger.log('性能监控已启动');
  }

  /**
   * 处理决策请求
   */
  public async makeDecision(request: DistributedDecisionRequest): Promise<DistributedDecisionResponse> {
    const startTime = Date.now();
    
    try {
      // 检查负载
      if (this.currentLoad >= this.maxCapacity) {
        throw new Error('节点负载已满');
      }
      
      // 增加负载
      this.currentLoad++;
      this.pendingRequests.set(request.requestId, request);
      
      // 执行决策
      const result = await this.executeDecision(request);
      
      // 构建响应
      const response: DistributedDecisionResponse = {
        requestId: request.requestId,
        entityId: request.entityId,
        result,
        processingTime: Date.now() - startTime,
        nodeId: this.nodeId,
        timestamp: Date.now()
      };
      
      // 更新统计
      this.updateStatistics(response.processingTime, false);
      
      // 发布结果
      await this.publishDecisionResult(response);
      
      return response;
      
    } catch (error) {
      this.logger.error(`决策执行失败 [${request.requestId}]:`, error);
      this.updateStatistics(Date.now() - startTime, true);
      throw error;
      
    } finally {
      // 减少负载
      this.currentLoad--;
      this.pendingRequests.delete(request.requestId);
    }
  }

  /**
   * 执行决策
   */
  private async executeDecision(request: DistributedDecisionRequest): Promise<DecisionResult> {
    // 检查是否有活跃的A/B测试
    const abTestVariant = await this.getABTestVariant(request);

    // 选择决策方法
    let result: DecisionResult;

    if (abTestVariant) {
      // 使用A/B测试变体
      result = await this.executeABTestDecision(request, abTestVariant);
    } else if (this.activeMLModel) {
      // 使用机器学习模型
      result = await this.executeMLDecision(request);
    } else {
      // 使用传统决策策略
      result = await this.executeTraditionalDecision(request);
    }

    // 记录决策结果用于学习
    await this.recordDecisionForLearning(request, result, abTestVariant);

    return result;
  }

  /**
   * 获取A/B测试变体
   */
  private async getABTestVariant(request: DistributedDecisionRequest): Promise<ABTestVariant | null> {
    for (const testId of this.activeABTests) {
      const test = this.abTests.get(testId);
      if (!test) continue;

      // 检查用户是否符合测试条件
      if (await this.isUserEligibleForTest(request, test)) {
        // 根据流量分配选择变体
        const variant = this.selectTestVariant(request, test);

        // 记录用户参与测试
        await this.recordTestParticipation(request, test, variant);

        return variant;
      }
    }

    return null;
  }

  /**
   * 执行A/B测试决策
   */
  private async executeABTestDecision(
    request: DistributedDecisionRequest,
    variant: ABTestVariant
  ): Promise<DecisionResult> {
    // 使用变体特定的策略和参数
    const strategy = this.decisionStrategies.get(variant.strategy);
    if (!strategy) {
      throw new Error(`未找到A/B测试策略: ${variant.strategy}`);
    }

    // 应用变体参数
    const modifiedContext = {
      ...request.context,
      abTestVariant: variant.id,
      abTestParameters: variant.parameters
    };

    return await this.executeDecisionWithStrategy(request, modifiedContext, strategy);
  }

  /**
   * 执行机器学习决策
   */
  private async executeMLDecision(request: DistributedDecisionRequest): Promise<DecisionResult> {
    const model = this.mlModels.get(this.activeMLModel!);
    if (!model) {
      throw new Error('活跃ML模型不存在');
    }

    // 特征提取
    const features = this.extractFeatures(request);

    // 模型预测
    const prediction = await this.predictWithModel(model, features);

    // 构建决策结果
    return this.buildMLDecisionResult(request, prediction, model);
  }

  /**
   * 执行传统决策
   */
  private async executeTraditionalDecision(request: DistributedDecisionRequest): Promise<DecisionResult> {
    const strategy = this.decisionStrategies.get(request.strategy || 'default');
    if (!strategy) {
      throw new Error(`未找到决策策略: ${request.strategy}`);
    }

    return await this.executeDecisionWithStrategy(request, request.context, strategy);
  }

  /**
   * 使用策略执行决策
   */
  private async executeDecisionWithStrategy(
    request: DistributedDecisionRequest,
    context: DecisionContext,
    strategy: DecisionStrategy
  ): Promise<DecisionResult> {
    // 模拟决策执行
    const selectedOption = request.options[0] || {
      id: 'default',
      name: '默认选项',
      description: '默认决策选项',
      type: 'default',
      cost: 0,
      benefit: 0.5,
      risk: 0.1,
      duration: 1000,
      requirements: [],
      consequences: [],
      confidence: 0.5
    };

    return {
      selectedOption,
      reasoning: '基于策略的最优选择',
      confidence: 0.8,
      alternatives: request.options.slice(1),
      executionPlan: [{
        id: 'step_1',
        action: 'execute',
        parameters: {},
        duration: 1000,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
  }

  /**
   * 处理决策请求消息
   */
  private async handleDecisionRequest(data: any): Promise<void> {
    const request = data as DistributedDecisionRequest;
    
    // 检查是否应该处理这个请求
    if (!this.shouldHandleRequest(request)) {
      return;
    }
    
    try {
      const response = await this.makeDecision(request);
      this.logger.log(`决策完成 [${request.requestId}]: ${response.result.selectedOption.name}`);
      
    } catch (error) {
      this.logger.error(`决策失败 [${request.requestId}]:`, error);
      
      // 发布错误响应
      await this.publishErrorResponse(request, error.message);
    }
  }

  /**
   * 处理直接请求
   */
  private async handleDirectRequest(data: any): Promise<void> {
    // 直接分配给当前节点的请求
    await this.handleDecisionRequest(data);
  }

  /**
   * 处理协调消息
   */
  private async handleCoordinationMessage(data: any): Promise<void> {
    switch (data.type) {
      case 'load_balance':
        await this.handleLoadBalance(data);
        break;
      case 'node_status_update':
        await this.handleNodeStatusUpdate(data);
        break;
      case 'strategy_update':
        await this.handleStrategyUpdate(data);
        break;
    }
  }

  /**
   * 处理负载均衡
   */
  private async handleLoadBalance(data: any): Promise<void> {
    // 实现负载均衡逻辑
    this.logger.log('处理负载均衡请求');
  }

  /**
   * 处理节点状态更新
   */
  private async handleNodeStatusUpdate(data: any): Promise<void> {
    const nodeStatus = data.status as NodeStatus;
    this.nodeStatuses.set(nodeStatus.nodeId, nodeStatus);
  }

  /**
   * 处理策略更新
   */
  private async handleStrategyUpdate(data: any): Promise<void> {
    this.coordinationStrategy = data.strategy;
    this.logger.log(`协调策略已更新: ${this.coordinationStrategy}`);
  }

  /**
   * 判断是否应该处理请求
   */
  private shouldHandleRequest(request: DistributedDecisionRequest): boolean {
    // 检查负载
    if (this.currentLoad >= this.maxCapacity) {
      return false;
    }
    
    // 检查优先级
    if (request.priority < 1) {
      return false;
    }
    
    // 根据协调策略决定
    switch (this.coordinationStrategy) {
      case CoordinationStrategy.LEAST_LOAD:
        return this.isLeastLoadedNode();
      case CoordinationStrategy.ROUND_ROBIN:
        return this.isMyTurn(request);
      default:
        return true;
    }
  }

  /**
   * 检查是否是负载最小的节点
   */
  private isLeastLoadedNode(): boolean {
    let minLoad = this.currentLoad;
    
    for (const status of this.nodeStatuses.values()) {
      if (status.isActive && status.load < minLoad) {
        minLoad = status.load;
      }
    }
    
    return this.currentLoad === minLoad;
  }

  /**
   * 检查是否轮到当前节点
   */
  private isMyTurn(request: DistributedDecisionRequest): boolean {
    // 简单的轮询实现
    const hash = this.hashString(request.entityId);
    const activeNodes = Array.from(this.nodeStatuses.values())
      .filter(status => status.isActive)
      .map(status => status.nodeId)
      .sort();
    
    if (activeNodes.length === 0) return true;
    
    const index = hash % activeNodes.length;
    return activeNodes[index] === this.nodeId;
  }

  /**
   * 字符串哈希
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 发布决策结果
   */
  private async publishDecisionResult(response: DistributedDecisionResponse): Promise<void> {
    await this.redis.publish('behavior:results', JSON.stringify(response));
    
    // 存储结果到缓存
    await this.redis.setex(
      `behavior:result:${response.requestId}`,
      300, // 5分钟过期
      JSON.stringify(response)
    );
  }

  /**
   * 发布错误响应
   */
  private async publishErrorResponse(request: DistributedDecisionRequest, error: string): Promise<void> {
    const errorResponse = {
      requestId: request.requestId,
      entityId: request.entityId,
      error,
      nodeId: this.nodeId,
      timestamp: Date.now()
    };
    
    await this.redis.publish('behavior:errors', JSON.stringify(errorResponse));
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(responseTime: number, isError: boolean): void {
    this.processedRequests++;
    this.totalResponseTime += responseTime;
    
    if (isError) {
      this.errorCount++;
    }
  }

  /**
   * 获取节点状态
   */
  public getNodeStatus(): NodeStatus {
    return {
      nodeId: this.nodeId,
      isActive: this.isActive,
      load: this.currentLoad,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: this.processedRequests,
      averageResponseTime: this.processedRequests > 0 ? this.totalResponseTime / this.processedRequests : 0,
      errorRate: this.processedRequests > 0 ? this.errorCount / this.processedRequests : 0
    };
  }

  /**
   * 获取所有节点状态
   */
  public async getAllNodeStatuses(): Promise<NodeStatus[]> {
    const nodeData = await this.redis.hgetall('behavior:nodes');
    const statuses: NodeStatus[] = [];
    
    for (const [nodeId, data] of Object.entries(nodeData)) {
      try {
        const status = JSON.parse(data) as NodeStatus;
        statuses.push(status);
      } catch (error) {
        this.logger.error(`解析节点状态失败 [${nodeId}]:`, error);
      }
    }
    
    return statuses;
  }

  /**
   * 设置协调策略
   */
  public async setCoordinationStrategy(strategy: CoordinationStrategy): Promise<void> {
    this.coordinationStrategy = strategy;

    // 广播策略更新
    await this.redis.publish('behavior:coordination', JSON.stringify({
      type: 'strategy_update',
      strategy,
      nodeId: this.nodeId,
      timestamp: Date.now()
    }));

    this.logger.log(`协调策略已设置: ${strategy}`);
  }

  /**
   * 检查用户是否符合A/B测试条件
   */
  private async isUserEligibleForTest(
    request: DistributedDecisionRequest,
    test: ABTestConfig
  ): Promise<boolean> {
    // 检查时间范围
    const now = Date.now();
    if (now < test.startTime || now > test.endTime) {
      return false;
    }

    // 检查分段规则
    if (test.segmentationRules) {
      // 这里应该实现具体的分段逻辑
      // 暂时返回true
    }

    return true;
  }

  /**
   * 选择测试变体
   */
  private selectTestVariant(request: DistributedDecisionRequest, test: ABTestConfig): ABTestVariant {
    // 使用用户ID的哈希值来确保一致性分配
    const hash = this.hashString(request.entityId + test.testId);
    const totalWeight = test.variants.reduce((sum, variant) => sum + variant.weight, 0);
    const randomValue = (hash % 1000) / 1000; // 0-1之间的值

    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += variant.weight / totalWeight;
      if (randomValue <= cumulativeWeight) {
        return variant;
      }
    }

    // 默认返回第一个变体
    return test.variants[0];
  }

  /**
   * 记录测试参与
   */
  private async recordTestParticipation(
    request: DistributedDecisionRequest,
    test: ABTestConfig,
    variant: ABTestVariant
  ): Promise<void> {
    const participation = {
      testId: test.testId,
      variantId: variant.id,
      entityId: request.entityId,
      sessionId: request.sessionId,
      timestamp: Date.now()
    };

    await this.redis.lpush(
      `behavior:ab_test:${test.testId}:participations`,
      JSON.stringify(participation)
    );
  }

  /**
   * 特征提取
   */
  private extractFeatures(request: DistributedDecisionRequest): number[] {
    // 从请求中提取特征向量
    const features: number[] = [];

    // 上下文特征
    if (request.context) {
      // 情感状态特征
      if (request.context.emotionalState) {
        features.push(
          request.context.emotionalState.intensity || 0,
          request.context.emotionalState.stress || 0,
          request.context.emotionalState.confidence || 0,
          request.context.emotionalState.motivation || 0
        );
      } else {
        features.push(0, 0, 0, 0);
      }

      // 环境状态特征
      if (request.context.environmentState) {
        features.push(
          request.context.environmentState.temperature / 50 || 0, // 归一化温度
          request.context.environmentState.visibility / 1000 || 0, // 归一化可见度
          request.context.environmentState.obstacles?.length || 0,
          request.context.environmentState.resources?.length || 0
        );
      } else {
        features.push(0, 0, 0, 0);
      }

      // 目标特征
      features.push(request.context.currentGoals?.length || 0);
    } else {
      // 如果没有上下文，填充默认值
      features.push(0, 0, 0, 0, 0, 0, 0, 0, 0);
    }

    // 选项特征
    features.push(request.options.length);

    // 时间特征
    const hour = new Date().getHours();
    features.push(hour / 24); // 归一化小时

    // 优先级特征
    features.push(request.priority / 5); // 归一化优先级

    return features;
  }

  /**
   * 使用模型进行预测
   */
  private async predictWithModel(model: MLModelConfig, features: number[]): Promise<any> {
    // 这里应该调用实际的ML模型
    // 暂时使用模拟预测

    switch (model.type) {
      case MLModelType.DECISION_TREE:
        return this.predictWithDecisionTree(features, model.parameters);
      case MLModelType.RANDOM_FOREST:
        return this.predictWithRandomForest(features, model.parameters);
      case MLModelType.NEURAL_NETWORK:
        return this.predictWithNeuralNetwork(features, model.parameters);
      default:
        return this.predictWithDecisionTree(features, model.parameters);
    }
  }

  /**
   * 决策树预测
   */
  private predictWithDecisionTree(features: number[], parameters: any): any {
    // 简化的决策树逻辑
    const [urgency, importance, complexity] = features;

    if (urgency > 0.7) {
      return { optionIndex: 0, confidence: 0.9 };
    } else if (importance > 0.8) {
      return { optionIndex: 1, confidence: 0.8 };
    } else {
      return { optionIndex: 0, confidence: 0.6 };
    }
  }

  /**
   * 随机森林预测
   */
  private predictWithRandomForest(features: number[], parameters: any): any {
    // 简化的随机森林逻辑
    const predictions = [];

    // 模拟多个决策树的预测
    for (let i = 0; i < 5; i++) {
      predictions.push(this.predictWithDecisionTree(features, parameters));
    }

    // 投票决定最终结果
    const votes = predictions.reduce((acc, pred) => {
      acc[pred.optionIndex] = (acc[pred.optionIndex] || 0) + 1;
      return acc;
    }, {});

    const bestOption = Object.keys(votes).reduce((a, b) =>
      votes[a] > votes[b] ? a : b
    );

    return {
      optionIndex: parseInt(bestOption),
      confidence: votes[bestOption] / predictions.length
    };
  }

  /**
   * 神经网络预测
   */
  private predictWithNeuralNetwork(features: number[], parameters: any): any {
    // 简化的神经网络逻辑
    // 这里应该使用实际的神经网络库

    let output = 0;
    for (let i = 0; i < features.length; i++) {
      output += features[i] * (0.5 + Math.random() * 0.5); // 模拟权重
    }

    const sigmoid = 1 / (1 + Math.exp(-output));
    const optionIndex = sigmoid > 0.5 ? 1 : 0;

    return { optionIndex, confidence: Math.abs(sigmoid - 0.5) * 2 };
  }

  /**
   * 定期清理过期数据
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredData(): Promise<void> {
    try {
      // 清理过期的节点状态
      const nodeData = await this.redis.hgetall('behavior:nodes');
      const now = Date.now();
      
      for (const [nodeId, data] of Object.entries(nodeData)) {
        try {
          const status = JSON.parse(data) as NodeStatus;
          
          // 如果节点超过1分钟没有心跳，标记为不活跃
          if (now - status.lastHeartbeat > 60000) {
            status.isActive = false;
            await this.redis.hset('behavior:nodes', nodeId, JSON.stringify(status));
            this.logger.warn(`节点 ${nodeId} 已标记为不活跃`);
          }
        } catch (error) {
          // 删除无效的节点数据
          await this.redis.hdel('behavior:nodes', nodeId);
          this.logger.warn(`删除无效节点数据: ${nodeId}`);
        }
      }
      
      this.logger.log('过期数据清理完成');
      
    } catch (error) {
      this.logger.error('数据清理失败:', error);
    }
  }

  /**
   * 构建ML决策结果
   */
  private buildMLDecisionResult(
    request: DistributedDecisionRequest,
    prediction: any,
    model: MLModelConfig
  ): DecisionResult {
    const selectedOption = request.options[prediction.optionIndex] || request.options[0] || {
      id: 'default',
      name: '默认选项',
      description: 'ML模型默认选项',
      type: 'default',
      cost: 0,
      benefit: 0.5,
      risk: 0.1,
      duration: 1000,
      requirements: [],
      consequences: [],
      confidence: prediction.confidence
    };

    return {
      selectedOption,
      reasoning: `基于${model.name}的机器学习预测`,
      confidence: prediction.confidence,
      alternatives: request.options.filter((_, index) => index !== prediction.optionIndex),
      executionPlan: [{
        id: 'ml_step_1',
        action: 'execute_ml_decision',
        parameters: { modelId: model.modelId, prediction },
        duration: 1000,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
  }

  /**
   * 记录决策用于学习
   */
  private async recordDecisionForLearning(
    request: DistributedDecisionRequest,
    result: DecisionResult,
    abTestVariant?: ABTestVariant
  ): Promise<void> {
    const learningRecord = {
      requestId: request.requestId,
      entityId: request.entityId,
      context: request.context,
      options: request.options,
      selectedOption: result.selectedOption,
      confidence: result.confidence,
      abTestVariant: abTestVariant?.id,
      modelUsed: this.activeMLModel,
      timestamp: Date.now()
    };

    await this.redis.lpush(
      'behavior:learning_data',
      JSON.stringify(learningRecord)
    );

    // 限制学习数据的大小
    await this.redis.ltrim('behavior:learning_data', 0, 10000);
  }

  /**
   * 收集性能指标
   */
  private collectPerformanceMetrics(): void {
    this.currentMetrics = {
      accuracy: this.calculateAccuracy(),
      responseTime: this.processedRequests > 0 ? this.totalResponseTime / this.processedRequests : 0,
      userSatisfaction: this.calculateUserSatisfaction(),
      conversionRate: this.calculateConversionRate(),
      errorRate: this.processedRequests > 0 ? this.errorCount / this.processedRequests : 0,
      throughput: this.processedRequests,
      timestamp: Date.now()
    };

    this.metricsHistory.push(this.currentMetrics);

    // 保持最近24小时的数据
    if (this.metricsHistory.length > 1440) { // 24 * 60 分钟
      this.metricsHistory.shift();
    }
  }

  /**
   * 计算准确率
   */
  private calculateAccuracy(): number {
    // 这里应该基于实际的反馈数据计算
    // 暂时返回模拟值
    return 0.85 + Math.random() * 0.1;
  }

  /**
   * 计算用户满意度
   */
  private calculateUserSatisfaction(): number {
    // 这里应该基于用户反馈计算
    // 暂时返回模拟值
    return 0.8 + Math.random() * 0.15;
  }

  /**
   * 计算转化率
   */
  private calculateConversionRate(): number {
    // 这里应该基于业务指标计算
    // 暂时返回模拟值
    return 0.75 + Math.random() * 0.2;
  }

  /**
   * 分析性能趋势
   */
  private analyzePerformanceTrends(): void {
    if (this.metricsHistory.length < 2) return;

    const recent = this.metricsHistory.slice(-60); // 最近1小时
    const previous = this.metricsHistory.slice(-120, -60); // 前1小时

    if (previous.length === 0) return;

    const recentAvg = this.calculateAverageMetrics(recent);
    const previousAvg = this.calculateAverageMetrics(previous);

    // 检查性能下降
    if (recentAvg.accuracy < previousAvg.accuracy * 0.9) {
      this.logger.warn('检测到准确率下降，可能需要重新训练模型');
      this.eventEmitter.emit('performance.accuracy_drop', {
        current: recentAvg.accuracy,
        previous: previousAvg.accuracy
      });
    }

    if (recentAvg.responseTime > previousAvg.responseTime * 1.5) {
      this.logger.warn('检测到响应时间增加，可能需要优化');
      this.eventEmitter.emit('performance.response_time_increase', {
        current: recentAvg.responseTime,
        previous: previousAvg.responseTime
      });
    }
  }

  /**
   * 计算平均指标
   */
  private calculateAverageMetrics(metrics: DecisionMetrics[]): DecisionMetrics {
    if (metrics.length === 0) {
      return this.currentMetrics;
    }

    const sum = metrics.reduce((acc, metric) => ({
      accuracy: acc.accuracy + metric.accuracy,
      responseTime: acc.responseTime + metric.responseTime,
      userSatisfaction: acc.userSatisfaction + metric.userSatisfaction,
      conversionRate: acc.conversionRate + metric.conversionRate,
      errorRate: acc.errorRate + metric.errorRate,
      throughput: acc.throughput + metric.throughput,
      timestamp: 0
    }), {
      accuracy: 0,
      responseTime: 0,
      userSatisfaction: 0,
      conversionRate: 0,
      errorRate: 0,
      throughput: 0,
      timestamp: 0
    });

    const count = metrics.length;
    return {
      accuracy: sum.accuracy / count,
      responseTime: sum.responseTime / count,
      userSatisfaction: sum.userSatisfaction / count,
      conversionRate: sum.conversionRate / count,
      errorRate: sum.errorRate / count,
      throughput: sum.throughput / count,
      timestamp: Date.now()
    };
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): DecisionMetrics {
    return this.currentMetrics;
  }

  /**
   * 获取性能历史
   */
  public getPerformanceHistory(hours: number = 24): DecisionMetrics[] {
    const cutoff = Date.now() - (hours * 60 * 60 * 1000);
    return this.metricsHistory.filter(metric => metric.timestamp > cutoff);
  }

  /**
   * 创建A/B测试
   */
  public async createABTest(config: ABTestConfig): Promise<void> {
    this.abTests.set(config.testId, config);

    if (config.isActive) {
      this.activeABTests.add(config.testId);
    }

    // 保存到Redis
    await this.redis.hset(
      'behavior:ab_tests',
      config.testId,
      JSON.stringify(config)
    );

    this.logger.log(`A/B测试已创建: ${config.testId}`);
  }

  /**
   * 停止A/B测试
   */
  public async stopABTest(testId: string): Promise<void> {
    const test = this.abTests.get(testId);
    if (test) {
      test.isActive = false;
      test.endTime = Date.now();

      this.activeABTests.delete(testId);

      // 更新Redis
      await this.redis.hset(
        'behavior:ab_tests',
        testId,
        JSON.stringify(test)
      );

      this.logger.log(`A/B测试已停止: ${testId}`);
    }
  }

  /**
   * 获取A/B测试结果
   */
  public async getABTestResults(testId: string): Promise<any> {
    const participations = await this.redis.lrange(
      `behavior:ab_test:${testId}:participations`,
      0,
      -1
    );

    const results = {
      testId,
      totalParticipants: participations.length,
      variantResults: new Map<string, any>()
    };

    // 分析每个变体的结果
    for (const participationData of participations) {
      try {
        const participation = JSON.parse(participationData);
        // 这里应该收集更多的业务指标
        // 暂时只统计参与数量

        if (!results.variantResults.has(participation.variantId)) {
          results.variantResults.set(participation.variantId, {
            participants: 0,
            conversions: 0,
            satisfaction: 0
          });
        }

        const variantResult = results.variantResults.get(participation.variantId);
        variantResult.participants++;

      } catch (error) {
        this.logger.error('解析A/B测试参与数据失败:', error);
      }
    }

    return results;
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭分布式行为决策服务...');

    this.isActive = false;

    // 注销节点
    await this.redis.hdel('behavior:nodes', this.nodeId);

    // 关闭连接
    this.redis.disconnect();
    this.pubsub.disconnect();

    this.logger.log('分布式行为决策服务已关闭');
  }
}
