import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisModule } from '@nestjs-modules/ioredis';
import { UITemplateModule } from './modules/ui-template/ui-template.module';
import { UIConfigModule } from './modules/ui-config/ui-config.module';
import { UIThemeModule } from './modules/ui-theme/ui-theme.module';
import { UIComponentModule } from './modules/ui-component/ui-component.module';
import { UIVersionModule } from './modules/ui-version/ui-version.module';
import { UIAnalyticsModule } from './modules/ui-analytics/ui-analytics.module';
import { WebSocketModule } from './modules/websocket/websocket.module';
import { PreviewModule } from './modules/preview/preview.module';
import { AuthModule } from './modules/auth/auth.module';
import { HealthModule } from './modules/health/health.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      useFactory: () => ({
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/dl-ui-service',
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
    }),

    // Redis模块
    RedisModule.forRootAsync({
      useFactory: () => ({
        config: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          db: parseInt(process.env.REDIS_DB || '0'),
        },
      }),
    }),

    // 业务模块
    UITemplateModule,
    UIConfigModule,
    UIThemeModule,
    UIComponentModule,
    UIVersionModule,
    UIAnalyticsModule,
    WebSocketModule,
    PreviewModule,
    AuthModule,
    HealthModule,
  ],
})
export class AppModule {}
