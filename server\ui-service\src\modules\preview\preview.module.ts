import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { PreviewService } from './preview.service';
import { PreviewController } from './preview.controller';
import { PreviewGateway } from './preview.gateway';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule
  ],
  controllers: [PreviewController],
  providers: [PreviewService, PreviewGateway],
  exports: [PreviewService]
})
export class PreviewModule {}
