/**
 * 性能监控服务
 * 
 * 提供全面的边缘计算性能监控、指标收集、分析和告警功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';

/**
 * 性能指标类型
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  SUMMARY = 'summary'
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
  name: string;
  type: MetricType;
  value: number;
  timestamp: number;
  labels: Record<string, string>;
  unit: string;
  description: string;
}

/**
 * 系统资源指标
 */
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    errors: number;
  };
}

/**
 * 应用性能指标
 */
export interface ApplicationMetrics {
  requests: {
    total: number;
    rate: number;
    errors: number;
    errorRate: number;
  };
  response: {
    averageTime: number;
    p50: number;
    p95: number;
    p99: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
    size: number;
    evictions: number;
  };
  database: {
    connections: number;
    queries: number;
    slowQueries: number;
    averageQueryTime: number;
  };
}

/**
 * 边缘节点性能
 */
export interface EdgeNodePerformance {
  nodeId: string;
  location: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  systemMetrics: SystemMetrics;
  applicationMetrics: ApplicationMetrics;
  customMetrics: PerformanceMetric[];
  lastUpdate: number;
  uptime: number;
}

/**
 * 告警规则
 */
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'ne' | 'gte' | 'lte';
  threshold: number;
  duration: number;
  severity: 'info' | 'warning' | 'critical';
  enabled: boolean;
  actions: AlertAction[];
}

/**
 * 告警动作
 */
export interface AlertAction {
  type: 'email' | 'webhook' | 'sms' | 'slack';
  config: Record<string, any>;
}

/**
 * 告警事件
 */
export interface AlertEvent {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: string;
  message: string;
  metric: string;
  value: number;
  threshold: number;
  nodeId: string;
  timestamp: number;
  resolved: boolean;
  resolvedAt?: number;
}

/**
 * 性能趋势
 */
export interface PerformanceTrend {
  metric: string;
  timeRange: string;
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  changeRate: number;
  prediction: number[];
  confidence: number;
}

/**
 * 性能监控服务
 */
@Injectable()
export class PerformanceMonitorService {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  
  // 性能数据存储
  private nodePerformance = new Map<string, EdgeNodePerformance>();
  private metricsHistory = new Map<string, PerformanceMetric[]>();
  
  // 告警管理
  private alertRules = new Map<string, AlertRule>();
  private activeAlerts = new Map<string, AlertEvent>();
  private alertHistory: AlertEvent[] = [];
  
  // 配置
  private config = {
    metricsRetentionDays: 30,
    metricsCollectionInterval: 30000, // 30秒
    alertCheckInterval: 60000, // 1分钟
    trendAnalysisInterval: 300000, // 5分钟
    maxMetricsPerNode: 10000,
    maxAlertHistory: 1000
  };
  
  // 统计数据
  private stats = {
    totalMetricsCollected: 0,
    totalAlertsTriggered: 0,
    totalAlertsResolved: 0,
    nodesMonitored: 0,
    rulesActive: 0
  };

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly redis: Redis
  ) {
    this.initializeMonitor();
  }

  /**
   * 初始化性能监控器
   */
  private async initializeMonitor(): Promise<void> {
    try {
      // 加载告警规则
      await this.loadAlertRules();
      
      // 启动指标收集
      this.startMetricsCollection();
      
      // 启动告警检查
      this.startAlertChecking();
      
      // 启动趋势分析
      this.startTrendAnalysis();
      
      this.logger.log('性能监控器初始化完成');
      
    } catch (error) {
      this.logger.error('性能监控器初始化失败:', error);
    }
  }

  /**
   * 收集节点性能指标
   */
  public async collectNodeMetrics(nodeId: string): Promise<void> {
    try {
      const systemMetrics = await this.collectSystemMetrics(nodeId);
      const applicationMetrics = await this.collectApplicationMetrics(nodeId);
      const customMetrics = await this.collectCustomMetrics(nodeId);
      
      const nodePerformance: EdgeNodePerformance = {
        nodeId,
        location: await this.getNodeLocation(nodeId),
        status: this.calculateNodeStatus(systemMetrics, applicationMetrics),
        systemMetrics,
        applicationMetrics,
        customMetrics,
        lastUpdate: Date.now(),
        uptime: await this.getNodeUptime(nodeId)
      };
      
      this.nodePerformance.set(nodeId, nodePerformance);
      this.stats.totalMetricsCollected++;
      
      // 存储历史数据
      await this.storeMetricsHistory(nodeId, nodePerformance);
      
      // 发布事件
      this.eventEmitter.emit('metrics.collected', { nodeId, metrics: nodePerformance });
      
    } catch (error) {
      this.logger.error(`收集节点指标失败 [${nodeId}]:`, error);
    }
  }

  /**
   * 添加自定义指标
   */
  public async addCustomMetric(
    nodeId: string,
    metric: PerformanceMetric
  ): Promise<void> {
    const nodePerf = this.nodePerformance.get(nodeId);
    if (nodePerf) {
      nodePerf.customMetrics.push(metric);
      
      // 限制指标数量
      if (nodePerf.customMetrics.length > this.config.maxMetricsPerNode) {
        nodePerf.customMetrics.shift();
      }
    }
    
    // 存储到历史记录
    const key = `${nodeId}:${metric.name}`;
    let history = this.metricsHistory.get(key) || [];
    history.push(metric);
    
    // 限制历史记录大小
    if (history.length > 1000) {
      history = history.slice(-500);
    }
    
    this.metricsHistory.set(key, history);
  }

  /**
   * 创建告警规则
   */
  public async createAlertRule(rule: AlertRule): Promise<void> {
    this.alertRules.set(rule.id, rule);
    this.stats.rulesActive = this.alertRules.size;
    
    // 保存到Redis
    await this.redis.hset('alert_rules', rule.id, JSON.stringify(rule));
    
    this.logger.log(`创建告警规则: ${rule.name}`);
  }

  /**
   * 检查告警条件
   */
  public async checkAlerts(): Promise<void> {
    for (const [ruleId, rule] of this.alertRules.entries()) {
      if (!rule.enabled) continue;
      
      try {
        await this.evaluateAlertRule(rule);
      } catch (error) {
        this.logger.error(`告警规则评估失败 [${rule.name}]:`, error);
      }
    }
  }

  /**
   * 评估告警规则
   */
  private async evaluateAlertRule(rule: AlertRule): Promise<void> {
    for (const [nodeId, nodePerf] of this.nodePerformance.entries()) {
      const metricValue = this.getMetricValue(nodePerf, rule.metric);
      
      if (metricValue !== null && this.evaluateCondition(metricValue, rule)) {
        // 检查是否已经有活跃告警
        const alertKey = `${rule.id}:${nodeId}`;
        
        if (!this.activeAlerts.has(alertKey)) {
          await this.triggerAlert(rule, nodeId, metricValue);
        }
      } else {
        // 检查是否需要解决告警
        const alertKey = `${rule.id}:${nodeId}`;
        const activeAlert = this.activeAlerts.get(alertKey);
        
        if (activeAlert && !activeAlert.resolved) {
          await this.resolveAlert(activeAlert);
        }
      }
    }
  }

  /**
   * 触发告警
   */
  private async triggerAlert(
    rule: AlertRule,
    nodeId: string,
    value: number
  ): Promise<void> {
    const alertEvent: AlertEvent = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      ruleId: rule.id,
      ruleName: rule.name,
      severity: rule.severity,
      message: `${rule.description}: ${rule.metric} = ${value} (threshold: ${rule.threshold})`,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      nodeId,
      timestamp: Date.now(),
      resolved: false
    };
    
    const alertKey = `${rule.id}:${nodeId}`;
    this.activeAlerts.set(alertKey, alertEvent);
    this.alertHistory.push(alertEvent);
    this.stats.totalAlertsTriggered++;
    
    // 限制历史记录大小
    if (this.alertHistory.length > this.config.maxAlertHistory) {
      this.alertHistory.shift();
    }
    
    this.logger.warn(`触发告警: ${alertEvent.message}`);
    
    // 执行告警动作
    for (const action of rule.actions) {
      await this.executeAlertAction(action, alertEvent);
    }
    
    // 发布事件
    this.eventEmitter.emit('alert.triggered', { alert: alertEvent });
  }

  /**
   * 解决告警
   */
  private async resolveAlert(alertEvent: AlertEvent): Promise<void> {
    alertEvent.resolved = true;
    alertEvent.resolvedAt = Date.now();
    
    const alertKey = `${alertEvent.ruleId}:${alertEvent.nodeId}`;
    this.activeAlerts.delete(alertKey);
    this.stats.totalAlertsResolved++;
    
    this.logger.log(`解决告警: ${alertEvent.message}`);
    
    // 发布事件
    this.eventEmitter.emit('alert.resolved', { alert: alertEvent });
  }

  /**
   * 分析性能趋势
   */
  public async analyzePerformanceTrends(): Promise<PerformanceTrend[]> {
    const trends: PerformanceTrend[] = [];
    
    for (const [key, history] of this.metricsHistory.entries()) {
      if (history.length < 10) continue; // 需要足够的数据点
      
      const trend = this.calculateTrend(history);
      if (trend) {
        trends.push(trend);
      }
    }
    
    return trends;
  }

  /**
   * 计算趋势
   */
  private calculateTrend(metrics: PerformanceMetric[]): PerformanceTrend | null {
    if (metrics.length < 2) return null;
    
    const values = metrics.map(m => m.value);
    const timeRange = metrics[metrics.length - 1].timestamp - metrics[0].timestamp;
    
    // 简单线性回归计算趋势
    const n = values.length;
    const sumX = values.reduce((sum, _, i) => sum + i, 0);
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
    const sumXX = values.reduce((sum, _, i) => sum + i * i, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const changeRate = slope / (sumY / n) * 100; // 百分比变化率
    
    let trendDirection: 'increasing' | 'decreasing' | 'stable' | 'volatile';
    if (Math.abs(changeRate) < 5) {
      trendDirection = 'stable';
    } else if (changeRate > 0) {
      trendDirection = 'increasing';
    } else {
      trendDirection = 'decreasing';
    }
    
    // 检查波动性
    const variance = this.calculateVariance(values);
    const mean = sumY / n;
    const cv = Math.sqrt(variance) / mean; // 变异系数
    
    if (cv > 0.3) {
      trendDirection = 'volatile';
    }
    
    return {
      metric: metrics[0].name,
      timeRange: `${Math.round(timeRange / 60000)}min`,
      trend: trendDirection,
      changeRate,
      prediction: this.predictFutureValues(values, 5),
      confidence: Math.max(0.1, 1 - cv)
    };
  }

  /**
   * 启动指标收集
   */
  private startMetricsCollection(): void {
    setInterval(async () => {
      const nodeIds = Array.from(this.nodePerformance.keys());
      for (const nodeId of nodeIds) {
        await this.collectNodeMetrics(nodeId);
      }
    }, this.config.metricsCollectionInterval);
  }

  /**
   * 启动告警检查
   */
  private startAlertChecking(): void {
    setInterval(async () => {
      await this.checkAlerts();
    }, this.config.alertCheckInterval);
  }

  /**
   * 启动趋势分析
   */
  private startTrendAnalysis(): void {
    setInterval(async () => {
      const trends = await this.analyzePerformanceTrends();
      this.eventEmitter.emit('trends.analyzed', { trends });
    }, this.config.trendAnalysisInterval);
  }

  // 辅助方法实现...
  private async loadAlertRules(): Promise<void> {
    // 从Redis加载告警规则
    const rules = await this.redis.hgetall('alert_rules');
    for (const [id, ruleData] of Object.entries(rules)) {
      try {
        const rule = JSON.parse(ruleData) as AlertRule;
        this.alertRules.set(id, rule);
      } catch (error) {
        this.logger.error(`加载告警规则失败 [${id}]:`, error);
      }
    }
    this.stats.rulesActive = this.alertRules.size;
  }

  private async collectSystemMetrics(nodeId: string): Promise<SystemMetrics> {
    // 收集系统指标
    return {
      cpu: { usage: 45.5, cores: 8, loadAverage: [1.2, 1.5, 1.8] },
      memory: { total: 16000000000, used: 8000000000, free: 8000000000, usage: 50 },
      disk: { total: 1000000000000, used: 500000000000, free: 500000000000, usage: 50, iops: 1000 },
      network: { bytesIn: 1000000, bytesOut: 2000000, packetsIn: 1000, packetsOut: 2000, errors: 0 }
    };
  }

  private async collectApplicationMetrics(nodeId: string): Promise<ApplicationMetrics> {
    // 收集应用指标
    return {
      requests: { total: 10000, rate: 100, errors: 10, errorRate: 0.1 },
      response: { averageTime: 150, p50: 100, p95: 300, p99: 500 },
      cache: { hitRate: 0.85, missRate: 0.15, size: 1000, evictions: 10 },
      database: { connections: 20, queries: 1000, slowQueries: 5, averageQueryTime: 50 }
    };
  }

  private async collectCustomMetrics(nodeId: string): Promise<PerformanceMetric[]> {
    // 收集自定义指标
    return [];
  }

  private async getNodeLocation(nodeId: string): Promise<string> {
    return 'us-east-1';
  }

  private calculateNodeStatus(
    systemMetrics: SystemMetrics,
    applicationMetrics: ApplicationMetrics
  ): 'healthy' | 'warning' | 'critical' | 'offline' {
    if (systemMetrics.cpu.usage > 90 || systemMetrics.memory.usage > 90) {
      return 'critical';
    }
    if (systemMetrics.cpu.usage > 70 || systemMetrics.memory.usage > 70) {
      return 'warning';
    }
    return 'healthy';
  }

  private async getNodeUptime(nodeId: string): Promise<number> {
    return Date.now() - 86400000; // 1天前
  }

  private async storeMetricsHistory(nodeId: string, metrics: EdgeNodePerformance): Promise<void> {
    // 存储指标历史
    const key = `metrics_history:${nodeId}`;
    await this.redis.lpush(key, JSON.stringify(metrics));
    await this.redis.ltrim(key, 0, 1000); // 保留最近1000条记录
  }

  private getMetricValue(nodePerf: EdgeNodePerformance, metricPath: string): number | null {
    // 根据路径获取指标值
    const parts = metricPath.split('.');
    let value: any = nodePerf;
    
    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return null;
      }
    }
    
    return typeof value === 'number' ? value : null;
  }

  private evaluateCondition(value: number, rule: AlertRule): boolean {
    switch (rule.condition) {
      case 'gt': return value > rule.threshold;
      case 'lt': return value < rule.threshold;
      case 'eq': return value === rule.threshold;
      case 'ne': return value !== rule.threshold;
      case 'gte': return value >= rule.threshold;
      case 'lte': return value <= rule.threshold;
      default: return false;
    }
  }

  private async executeAlertAction(action: AlertAction, alert: AlertEvent): Promise<void> {
    // 执行告警动作
    this.logger.log(`执行告警动作: ${action.type} for ${alert.message}`);
  }

  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }

  private predictFutureValues(values: number[], steps: number): number[] {
    // 简单的线性预测
    if (values.length < 2) return [];
    
    const lastValue = values[values.length - 1];
    const secondLastValue = values[values.length - 2];
    const trend = lastValue - secondLastValue;
    
    const predictions: number[] = [];
    for (let i = 1; i <= steps; i++) {
      predictions.push(lastValue + trend * i);
    }
    
    return predictions;
  }

  /**
   * 获取节点性能数据
   */
  public getNodePerformance(nodeId: string): EdgeNodePerformance | undefined {
    return this.nodePerformance.get(nodeId);
  }

  /**
   * 获取所有节点性能数据
   */
  public getAllNodePerformance(): EdgeNodePerformance[] {
    return Array.from(this.nodePerformance.values());
  }

  /**
   * 获取活跃告警
   */
  public getActiveAlerts(): AlertEvent[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): any {
    return {
      stats: this.stats,
      activeAlerts: this.activeAlerts.size,
      totalRules: this.alertRules.size,
      nodesMonitored: this.nodePerformance.size
    };
  }

  /**
   * 定期清理历史数据
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private async cleanupHistoricalData(): Promise<void> {
    const cutoffTime = Date.now() - (this.config.metricsRetentionDays * 24 * 60 * 60 * 1000);
    
    for (const [key, history] of this.metricsHistory.entries()) {
      const filteredHistory = history.filter(metric => metric.timestamp > cutoffTime);
      this.metricsHistory.set(key, filteredHistory);
    }
    
    // 清理告警历史
    this.alertHistory = this.alertHistory.filter(alert => alert.timestamp > cutoffTime);
    
    this.logger.log('历史数据清理完成');
  }
}
