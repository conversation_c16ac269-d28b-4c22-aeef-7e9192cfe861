import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Model, Types } from 'mongoose';
import Redis from 'ioredis';
import { UIComponent, UIComponentDocument, ComponentType, ComponentStatus } from './schemas/ui-component.schema';
import { CreateUIComponentDto, UpdateUIComponentDto, QueryUIComponentDto } from './dto/ui-component.dto';

@Injectable()
export class UIComponentService {
  constructor(
    @InjectModel(UIComponent.name) private componentModel: Model<UIComponentDocument>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * 创建UI组件
   */
  async create(createComponentDto: CreateUIComponentDto, userId: Types.ObjectId): Promise<UIComponent> {
    try {
      // 检查组件名称是否已存在
      const existingComponent = await this.componentModel.findOne({
        name: createComponentDto.name,
        organizationId: createComponentDto.organizationId,
      });

      if (existingComponent) {
        throw new BadRequestException('组件名称已存在');
      }

      // 创建组件
      const component = new this.componentModel({
        ...createComponentDto,
        createdBy: userId,
        updatedBy: userId,
      });

      const savedComponent = await component.save();

      // 清除相关缓存
      await this.clearComponentCache(createComponentDto.type);

      return savedComponent;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('创建组件失败');
    }
  }

  /**
   * 查询UI组件列表
   */
  async findAll(queryDto: QueryUIComponentDto, userId: Types.ObjectId): Promise<{
    components: UIComponent[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {
      isActive: true,
    };

    // 添加过滤条件
    if (filters.type) query.type = filters.type;
    if (filters.status) query.status = filters.status;
    if (filters.organizationId) query.organizationId = filters.organizationId;
    if (filters.tags) query.tags = { $in: filters.tags.split(',') };
    if (typeof filters.isPublic === 'boolean') query.isPublic = filters.isPublic;

    // 添加搜索条件
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // 添加权限过滤
    query.$or = [
      { isPublic: true },
      { createdBy: userId },
      // 这里可以添加组织权限检查
    ];

    // 排序
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // 执行查询
    const [components, total] = await Promise.all([
      this.componentModel
        .find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email')
        .exec(),
      this.componentModel.countDocuments(query),
    ]);

    return {
      components,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单个UI组件
   */
  async findOne(id: string, userId: Types.ObjectId): Promise<UIComponent> {
    // 尝试从缓存获取
    const cacheKey = `ui_component:${id}`;
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      const component = JSON.parse(cached);
      if (this.canAccessComponent(component, userId)) {
        return component;
      }
    }

    // 从数据库获取
    const component = await this.componentModel
      .findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .exec();

    if (!component) {
      throw new NotFoundException('组件不存在');
    }

    // 检查访问权限
    if (!this.canAccessComponent(component, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 增加浏览次数
    await this.incrementViews(component);

    // 缓存组件
    await this.redis.setex(cacheKey, 1800, JSON.stringify(component));

    return component;
  }

  /**
   * 更新UI组件
   */
  async update(id: string, updateComponentDto: UpdateUIComponentDto, userId: Types.ObjectId): Promise<UIComponent> {
    const component = await this.componentModel.findById(id);

    if (!component) {
      throw new NotFoundException('组件不存在');
    }

    // 检查编辑权限
    if (!this.canEditComponent(component, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 更新组件
    Object.assign(component, updateComponentDto);
    component.updatedBy = userId;
    component.updatedAt = new Date();

    const updatedComponent = await component.save();

    // 清除缓存
    await this.redis.del(`ui_component:${id}`);
    await this.clearComponentCache(component.type);

    return updatedComponent;
  }

  /**
   * 删除UI组件
   */
  async remove(id: string, userId: Types.ObjectId): Promise<void> {
    const component = await this.componentModel.findById(id);

    if (!component) {
      throw new NotFoundException('组件不存在');
    }

    // 检查删除权限
    if (!this.canEditComponent(component, userId)) {
      throw new ForbiddenException('没有删除权限');
    }

    // 软删除
    await this.softDeleteComponent(component, userId);

    // 清除缓存
    await this.redis.del(`ui_component:${id}`);
    await this.clearComponentCache(component.type);
  }

  /**
   * 下载组件
   */
  async download(id: string, userId: Types.ObjectId): Promise<UIComponent> {
    const component = await this.findOne(id, userId);
    
    // 增加下载次数
    await this.incrementDownloads(component);

    return component;
  }

  /**
   * 检查组件访问权限
   */
  private canAccessComponent(component: UIComponent, userId: Types.ObjectId): boolean {
    if (component.isPublic) {
      return true;
    }

    return component.createdBy.equals(userId);
  }

  /**
   * 检查组件编辑权限
   */
  private canEditComponent(component: UIComponent, userId: Types.ObjectId): boolean {
    return component.createdBy.equals(userId);
  }

  /**
   * 增加浏览次数
   */
  private async incrementViews(component: any): Promise<void> {
    component.statistics = component.statistics || {};
    component.statistics.views = (component.statistics.views || 0) + 1;
    await component.save();
  }

  /**
   * 增加下载次数
   */
  private async incrementDownloads(component: any): Promise<void> {
    component.statistics = component.statistics || {};
    component.statistics.downloads = (component.statistics.downloads || 0) + 1;
    await component.save();
  }

  /**
   * 软删除组件
   */
  private async softDeleteComponent(component: any, userId: Types.ObjectId): Promise<void> {
    component.deletedAt = new Date();
    component.deletedBy = userId;
    component.isActive = false;
    await component.save();
  }

  /**
   * 发布组件
   */
  async publishComponent(id: string, userId: Types.ObjectId): Promise<UIComponent> {
    const component = await this.componentModel.findById(id);

    if (!component) {
      throw new NotFoundException('组件不存在');
    }

    // 检查发布权限
    if (!this.canEditComponent(component, userId)) {
      throw new ForbiddenException('没有发布权限');
    }

    // 验证组件完整性
    await this.validateComponentForPublish(component);

    // 更新状态为已发布
    component.status = ComponentStatus.PUBLISHED;
    component.publishedAt = new Date();
    component.publishedBy = userId;
    component.updatedBy = userId;
    component.updatedAt = new Date();

    const publishedComponent = await component.save();

    // 清除缓存
    await this.redis.del(`ui_component:${id}`);
    await this.clearComponentCache(component.type);

    return publishedComponent;
  }

  /**
   * 获取组件依赖
   */
  async getComponentDependencies(id: string, userId: Types.ObjectId): Promise<UIComponent[]> {
    const component = await this.findOne(id, userId);

    if (!component.dependencies || component.dependencies.length === 0) {
      return [];
    }

    // 获取依赖组件
    const dependencies = await this.componentModel
      .find({
        _id: { $in: component.dependencies },
        isActive: true
      })
      .populate('createdBy', 'username email')
      .exec();

    // 过滤用户有权限访问的依赖
    return dependencies.filter(dep => this.canAccessComponent(dep, userId));
  }

  /**
   * 获取组件使用者
   */
  async getComponentUsages(id: string, userId: Types.ObjectId): Promise<UIComponent[]> {
    const component = await this.findOne(id, userId);

    // 查找依赖此组件的其他组件
    const usages = await this.componentModel
      .find({
        dependencies: (component as any)._id,
        isActive: true
      })
      .populate('createdBy', 'username email')
      .exec();

    // 过滤用户有权限访问的组件
    return usages.filter(usage => this.canAccessComponent(usage, userId));
  }

  /**
   * 复制组件
   */
  async duplicateComponent(id: string, userId: Types.ObjectId, newName?: string): Promise<UIComponent> {
    const originalComponent = await this.findOne(id, userId);

    // 创建副本
    const duplicateData = {
      ...(originalComponent as any).toObject(),
      _id: undefined,
      name: newName || `${originalComponent.name} - 副本`,
      isPublic: false,
      status: ComponentStatus.DRAFT,
      createdBy: userId,
      updatedBy: userId,
      publishedAt: undefined,
      publishedBy: undefined,
      statistics: {
        views: 0,
        downloads: 0,
        likes: 0,
        uses: 0
      }
    };

    const duplicateComponent = new this.componentModel(duplicateData);
    const savedComponent = await duplicateComponent.save();

    // 清除相关缓存
    await this.clearComponentCache(originalComponent.type);

    return savedComponent;
  }

  /**
   * 获取组件统计信息
   */
  async getComponentStats(id: string, userId: Types.ObjectId): Promise<any> {
    const component = await this.findOne(id, userId);

    return {
      id: (component as any)._id,
      name: component.name,
      statistics: component.statistics || {
        views: 0,
        downloads: 0,
        likes: 0,
        uses: 0
      },
      dependencies: component.dependencies?.length || 0,
      usages: await this.getComponentUsageCount((component as any)._id),
      createdAt: component.createdAt,
      updatedAt: component.updatedAt,
      publishedAt: component.publishedAt
    };
  }

  /**
   * 搜索组件库
   */
  async searchComponentLibrary(query: string, filters: any = {}, userId: Types.ObjectId): Promise<UIComponent[]> {
    const searchQuery: any = {
      isActive: true,
      status: ComponentStatus.PUBLISHED,
      $or: [
        { isPublic: true },
        { createdBy: userId }
      ]
    };

    // 添加文本搜索
    if (query) {
      searchQuery.$text = { $search: query };
    }

    // 添加过滤条件
    if (filters.type) searchQuery.type = filters.type;
    if (filters.tags) searchQuery.tags = { $in: filters.tags };

    const components = await this.componentModel
      .find(searchQuery)
      .sort({ score: { $meta: 'textScore' } })
      .limit(50)
      .populate('createdBy', 'username email')
      .exec();

    return components;
  }

  /**
   * 验证组件发布条件
   */
  private async validateComponentForPublish(component: any): Promise<void> {
    const errors = [];

    // 检查必需字段
    if (!component.name) errors.push('组件名称不能为空');
    if (!component.description) errors.push('组件描述不能为空');
    if (!component.code) errors.push('组件代码不能为空');
    if (!component.props || Object.keys(component.props).length === 0) {
      errors.push('组件属性定义不能为空');
    }

    // 检查依赖是否都已发布
    if (component.dependencies && component.dependencies.length > 0) {
      const dependencies = await this.componentModel.find({
        _id: { $in: component.dependencies },
        status: { $ne: ComponentStatus.PUBLISHED }
      });

      if (dependencies.length > 0) {
        errors.push('存在未发布的依赖组件');
      }
    }

    if (errors.length > 0) {
      throw new BadRequestException(`发布验证失败: ${errors.join(', ')}`);
    }
  }

  /**
   * 获取组件使用次数
   */
  private async getComponentUsageCount(componentId: Types.ObjectId): Promise<number> {
    return this.componentModel.countDocuments({
      dependencies: componentId,
      isActive: true
    });
  }

  /**
   * 清除组件缓存
   */
  private async clearComponentCache(type: ComponentType): Promise<void> {
    const pattern = `ui_component*:${type}:*`;
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
