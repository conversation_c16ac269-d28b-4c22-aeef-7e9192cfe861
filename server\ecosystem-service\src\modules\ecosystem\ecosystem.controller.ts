import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { EcosystemPlatformService } from './ecosystem-platform.service';
import { DependencyManagerService, ServiceDependency } from './services/dependency-manager.service';
import { EcosystemHealthMonitorService } from './services/ecosystem-health-monitor.service';
import { ServiceDiscoveryService, ServiceInstance, ServiceQuery, LoadBalanceStrategy } from './services/service-discovery.service';
import { AutomatedDeploymentService, DeploymentConfig } from './services/automated-deployment.service';

@ApiTags('ecosystem')
@Controller('ecosystem')
export class EcosystemController {
  constructor(
    private readonly ecosystemService: EcosystemPlatformService,
    private readonly dependencyManager: DependencyManagerService,
    private readonly healthMonitor: EcosystemHealthMonitorService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly deploymentService: AutomatedDeploymentService,
  ) {}

  @Get('statistics')
  @ApiOperation({ summary: '获取生态系统统计信息' })
  @ApiResponse({ status: 200, description: '生态系统统计数据' })
  async getStatistics() {
    return this.ecosystemService.getEcosystemStatistics();
  }

  @Post('partners/register')
  @ApiOperation({ summary: '注册合作伙伴' })
  @ApiResponse({ status: 201, description: '合作伙伴注册成功' })
  async registerPartner(@Body() partnerApplication: any) {
    return this.ecosystemService.registerPartner(partnerApplication);
  }

  @Post('apis/publish')
  @ApiOperation({ summary: '发布API规范' })
  @ApiResponse({ status: 201, description: 'API规范发布成功' })
  async publishAPI(@Body() apiSpec: any) {
    return this.ecosystemService.publishAPISpecification(apiSpec);
  }

  @Post('applications/submit')
  @ApiOperation({ summary: '提交第三方应用' })
  @ApiResponse({ status: 201, description: '应用提交成功' })
  async submitApplication(@Body() appSubmission: any) {
    return this.ecosystemService.submitThirdPartyApplication(appSubmission);
  }

  @Post('standards/create')
  @ApiOperation({ summary: '创建行业标准' })
  @ApiResponse({ status: 201, description: '标准创建成功' })
  async createStandard(@Body() standardSpec: any) {
    return this.ecosystemService.createIndustryStandard(standardSpec);
  }

  // ==================== 服务依赖管理接口 (新增) ====================

  @Post('dependencies/services/register')
  @ApiOperation({ summary: '注册服务' })
  @ApiResponse({ status: 201, description: '服务注册成功' })
  async registerService(@Body() serviceInfo: any) {
    await this.dependencyManager.registerService(serviceInfo);
    return { success: true, message: '服务注册成功' };
  }

  @Delete('dependencies/services/:serviceId')
  @ApiOperation({ summary: '注销服务' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  @ApiResponse({ status: 200, description: '服务注销成功' })
  async unregisterService(@Param('serviceId') serviceId: string) {
    await this.dependencyManager.unregisterService(serviceId);
    return { success: true, message: '服务注销成功' };
  }

  @Post('dependencies/add')
  @ApiOperation({ summary: '添加服务依赖' })
  @ApiResponse({ status: 201, description: '依赖添加成功' })
  async addDependency(@Body() dependency: ServiceDependency) {
    await this.dependencyManager.addDependency(dependency);
    return { success: true, message: '依赖添加成功' };
  }

  @Delete('dependencies/:serviceId/:dependsOn')
  @ApiOperation({ summary: '移除服务依赖' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  @ApiParam({ name: 'dependsOn', description: '依赖服务ID' })
  @ApiResponse({ status: 200, description: '依赖移除成功' })
  async removeDependency(
    @Param('serviceId') serviceId: string,
    @Param('dependsOn') dependsOn: string
  ) {
    await this.dependencyManager.removeDependency(serviceId, dependsOn);
    return { success: true, message: '依赖移除成功' };
  }

  @Get('dependencies/graph')
  @ApiOperation({ summary: '获取依赖图' })
  @ApiResponse({ status: 200, description: '依赖图数据' })
  async getDependencyGraph() {
    const graph = await this.dependencyManager.getDependencyGraph();
    return { success: true, data: graph };
  }

  @Get('dependencies/services')
  @ApiOperation({ summary: '获取所有服务' })
  @ApiResponse({ status: 200, description: '服务列表' })
  async getAllServices() {
    const services = this.dependencyManager.getAllServices();
    return { success: true, data: services };
  }

  @Get('dependencies/services/:serviceId')
  @ApiOperation({ summary: '获取服务信息' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  @ApiResponse({ status: 200, description: '服务信息' })
  async getService(@Param('serviceId') serviceId: string) {
    const service = this.dependencyManager.getService(serviceId);
    return { success: true, data: service };
  }

  @Get('dependencies/services/:serviceId/dependencies')
  @ApiOperation({ summary: '获取服务依赖' })
  @ApiParam({ name: 'serviceId', description: '服务ID' })
  @ApiResponse({ status: 200, description: '服务依赖列表' })
  async getServiceDependencies(@Param('serviceId') serviceId: string) {
    const dependencies = this.dependencyManager.getServiceDependencies(serviceId);
    return { success: true, data: dependencies };
  }

  @Get('dependencies/conflicts')
  @ApiOperation({ summary: '获取依赖冲突' })
  @ApiResponse({ status: 200, description: '依赖冲突列表' })
  async getDependencyConflicts() {
    const conflicts = this.dependencyManager.getConflicts();
    return { success: true, data: conflicts };
  }

  // ==================== 生态健康监控接口 (新增) ====================

  @Get('health/metrics')
  @ApiOperation({ summary: '获取当前健康指标' })
  @ApiResponse({ status: 200, description: '健康指标数据' })
  async getCurrentHealthMetrics() {
    const metrics = this.healthMonitor.getCurrentMetrics();
    return { success: true, data: metrics };
  }

  @Get('health/history')
  @ApiOperation({ summary: '获取健康历史' })
  @ApiQuery({ name: 'hours', description: '历史小时数', required: false })
  @ApiResponse({ status: 200, description: '健康历史数据' })
  async getHealthHistory(@Query('hours') hours?: string) {
    const hoursNum = hours ? parseInt(hours) : 24;
    const history = this.healthMonitor.getHealthHistory(hoursNum);
    return { success: true, data: history };
  }

  @Get('health/issues')
  @ApiOperation({ summary: '获取活跃健康问题' })
  @ApiResponse({ status: 200, description: '健康问题列表' })
  async getActiveHealthIssues() {
    const issues = this.healthMonitor.getActiveIssues();
    return { success: true, data: issues };
  }

  @Post('health/issues/:issueId/resolve')
  @ApiOperation({ summary: '解决健康问题' })
  @ApiParam({ name: 'issueId', description: '问题ID' })
  @ApiResponse({ status: 200, description: '问题解决成功' })
  async resolveHealthIssue(@Param('issueId') issueId: string) {
    await this.healthMonitor.resolveIssue(issueId);
    return { success: true, message: '问题解决成功' };
  }

  // ==================== 服务发现接口 (新增) ====================

  @Post('discovery/instances/register')
  @ApiOperation({ summary: '注册服务实例' })
  @ApiResponse({ status: 201, description: '实例注册成功' })
  async registerServiceInstance(@Body() instance: ServiceInstance) {
    await this.serviceDiscovery.registerInstance(instance);
    return { success: true, message: '实例注册成功' };
  }

  @Delete('discovery/instances/:instanceId')
  @ApiOperation({ summary: '注销服务实例' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  @ApiResponse({ status: 200, description: '实例注销成功' })
  async deregisterServiceInstance(@Param('instanceId') instanceId: string) {
    await this.serviceDiscovery.deregisterInstance(instanceId);
    return { success: true, message: '实例注销成功' };
  }

  @Post('discovery/services/discover')
  @ApiOperation({ summary: '发现服务实例' })
  @ApiResponse({ status: 200, description: '服务实例列表' })
  async discoverServices(@Body() query: ServiceQuery) {
    const instances = await this.serviceDiscovery.discoverServices(query);
    return { success: true, data: instances };
  }

  @Get('discovery/services/:serviceName/instance')
  @ApiOperation({ summary: '获取服务实例（负载均衡）' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiQuery({ name: 'strategy', description: '负载均衡策略', required: false })
  @ApiResponse({ status: 200, description: '服务实例' })
  async getServiceInstance(
    @Param('serviceName') serviceName: string,
    @Query('strategy') strategy?: LoadBalanceStrategy
  ) {
    const instance = await this.serviceDiscovery.getServiceInstance(serviceName, strategy);
    return { success: true, data: instance };
  }

  @Post('discovery/instances/:instanceId/heartbeat')
  @ApiOperation({ summary: '服务实例心跳' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  @ApiResponse({ status: 200, description: '心跳成功' })
  async instanceHeartbeat(@Param('instanceId') instanceId: string) {
    await this.serviceDiscovery.heartbeat(instanceId);
    return { success: true, message: '心跳成功' };
  }

  @Get('discovery/services')
  @ApiOperation({ summary: '获取服务列表' })
  @ApiResponse({ status: 200, description: '服务列表' })
  async getServiceList() {
    const services = this.serviceDiscovery.getServiceList();
    return { success: true, data: services };
  }

  @Get('discovery/services/:serviceName/instances')
  @ApiOperation({ summary: '获取服务实例列表' })
  @ApiParam({ name: 'serviceName', description: '服务名称' })
  @ApiResponse({ status: 200, description: '实例列表' })
  async getServiceInstances(@Param('serviceName') serviceName: string) {
    const instances = this.serviceDiscovery.getServiceInstances(serviceName);
    return { success: true, data: instances };
  }

  @Get('discovery/instances/:instanceId')
  @ApiOperation({ summary: '获取实例详情' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  @ApiResponse({ status: 200, description: '实例详情' })
  async getInstance(@Param('instanceId') instanceId: string) {
    const instance = this.serviceDiscovery.getInstance(instanceId);
    return { success: true, data: instance };
  }

  // ==================== 自动化部署接口 (新增) ====================

  @Post('deployment/create')
  @ApiOperation({ summary: '创建部署' })
  @ApiResponse({ status: 201, description: '部署创建成功' })
  async createDeployment(@Body() config: DeploymentConfig) {
    const deploymentId = await this.deploymentService.createDeployment(config);
    return { success: true, data: { deploymentId }, message: '部署创建成功' };
  }

  @Get('deployment/:deploymentId')
  @ApiOperation({ summary: '获取部署记录' })
  @ApiParam({ name: 'deploymentId', description: '部署ID' })
  @ApiResponse({ status: 200, description: '部署记录' })
  async getDeployment(@Param('deploymentId') deploymentId: string) {
    const deployment = this.deploymentService.getDeployment(deploymentId);
    return { success: true, data: deployment };
  }

  @Get('deployment')
  @ApiOperation({ summary: '获取部署列表' })
  @ApiQuery({ name: 'applicationId', description: '应用ID', required: false })
  @ApiResponse({ status: 200, description: '部署列表' })
  async getDeployments(@Query('applicationId') applicationId?: string) {
    const deployments = this.deploymentService.getDeployments(applicationId);
    return { success: true, data: deployments };
  }

  @Post('deployment/:deploymentId/rollback')
  @ApiOperation({ summary: '执行回滚' })
  @ApiParam({ name: 'deploymentId', description: '部署ID' })
  @ApiResponse({ status: 200, description: '回滚执行成功' })
  async performRollback(
    @Param('deploymentId') deploymentId: string,
    @Body() body: { reason: string }
  ) {
    await this.deploymentService.performRollback(deploymentId, 'user', body.reason);
    return { success: true, message: '回滚执行成功' };
  }

  @Post('deployment/:deploymentId/cancel')
  @ApiOperation({ summary: '取消部署' })
  @ApiParam({ name: 'deploymentId', description: '部署ID' })
  @ApiResponse({ status: 200, description: '部署取消成功' })
  async cancelDeployment(@Param('deploymentId') deploymentId: string) {
    await this.deploymentService.cancelDeployment(deploymentId);
    return { success: true, message: '部署取消成功' };
  }
}
