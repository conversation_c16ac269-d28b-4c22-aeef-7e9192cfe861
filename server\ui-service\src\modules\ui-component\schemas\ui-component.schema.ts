import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type UIComponentDocument = UIComponent & Document;

/**
 * 组件类型
 */
export enum ComponentType {
  BASIC = 'basic',           // 基础组件
  LAYOUT = 'layout',         // 布局组件
  FORM = 'form',            // 表单组件
  DATA = 'data',            // 数据展示组件
  FEEDBACK = 'feedback',     // 反馈组件
  NAVIGATION = 'navigation', // 导航组件
  CUSTOM = 'custom',        // 自定义组件
}

/**
 * 组件状态
 */
export enum ComponentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DEPRECATED = 'deprecated',
  ARCHIVED = 'archived',
}

/**
 * 组件属性定义
 */
export interface ComponentProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
  required?: boolean;
  default?: any;
  description?: string;
  options?: any[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
}

/**
 * 组件事件定义
 */
export interface ComponentEvent {
  name: string;
  description?: string;
  parameters?: {
    name: string;
    type: string;
    description?: string;
  }[];
}

/**
 * 组件样式定义
 */
export interface ComponentStyle {
  selector: string;
  properties: {
    [key: string]: string | number;
  };
}

@Schema({
  timestamps: true,
  collection: 'ui_components',
})
export class UIComponent {
  @Prop({ required: true, maxlength: 100 })
  name: string;

  @Prop({ maxlength: 500 })
  description?: string;

  @Prop({ 
    type: String, 
    enum: Object.values(ComponentType),
    required: true,
    index: true 
  })
  type: ComponentType;

  @Prop({ 
    type: String, 
    enum: Object.values(ComponentStatus),
    default: ComponentStatus.DRAFT,
    index: true 
  })
  status: ComponentStatus;

  @Prop({ required: true })
  version: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: String, required: true })
  template: string; // HTML模板

  @Prop({ type: String })
  script?: string; // JavaScript代码

  @Prop({ type: String })
  style?: string; // CSS样式

  @Prop({ type: [Object], default: [] })
  properties: ComponentProperty[];

  @Prop({ type: [Object], default: [] })
  events: ComponentEvent[];

  @Prop({ type: [Object], default: [] })
  styles: ComponentStyle[];

  @Prop({ type: Object })
  metadata?: {
    author?: string;
    license?: string;
    homepage?: string;
    repository?: string;
    keywords?: string[];
    dependencies?: string[];
    peerDependencies?: string[];
    preview?: string;
    screenshots?: string[];
    documentation?: string;
  };

  @Prop({ type: Object, default: {} })
  config: {
    framework?: string; // 支持的框架 (vue, react, angular, vanilla)
    minVersion?: string;
    maxVersion?: string;
    responsive?: boolean;
    accessibility?: boolean;
    rtl?: boolean; // 右到左语言支持
  };

  @Prop({ type: Types.ObjectId, index: true })
  organizationId?: Types.ObjectId;

  @Prop({ default: true })
  isPublic: boolean;

  @Prop({ type: [Types.ObjectId], default: [] })
  dependencies: Types.ObjectId[];

  @Prop()
  publishedAt?: Date;

  @Prop({ type: Types.ObjectId })
  publishedBy?: Types.ObjectId;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  statistics: {
    downloads?: number;
    likes?: number;
    rating?: number;
    ratingCount?: number;
    views?: number;
    forks?: number;
  };

  @Prop({ type: Object, default: {} })
  settings: {
    allowFork?: boolean;
    allowComments?: boolean;
    allowRating?: boolean;
    requireApproval?: boolean;
  };

  @Prop({ type: Types.ObjectId, required: true })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true })
  updatedBy: Types.ObjectId;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: Types.ObjectId })
  deletedBy?: Types.ObjectId;
}

export const UIComponentSchema = SchemaFactory.createForClass(UIComponent);

// 创建索引
UIComponentSchema.index({ name: 1, organizationId: 1 }, { unique: true });
UIComponentSchema.index({ type: 1, status: 1 });
UIComponentSchema.index({ tags: 1 });
UIComponentSchema.index({ isPublic: 1, isActive: 1 });
UIComponentSchema.index({ 'statistics.rating': -1 });
UIComponentSchema.index({ 'statistics.downloads': -1 });
UIComponentSchema.index({ createdAt: -1 });
UIComponentSchema.index({ version: 1 });

// 添加文本搜索索引
UIComponentSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text',
  'metadata.keywords': 'text',
});

// 添加实例方法
UIComponentSchema.methods.canAccess = function(userId: Types.ObjectId, organizationId?: Types.ObjectId): boolean {
  if (this.isPublic) {
    return true;
  }
  
  if (this.organizationId && organizationId) {
    return this.organizationId.equals(organizationId);
  }
  
  return this.createdBy.equals(userId);
};

UIComponentSchema.methods.canEdit = function(userId: Types.ObjectId): boolean {
  return this.createdBy.equals(userId);
};

UIComponentSchema.methods.incrementDownloads = function(): Promise<UIComponent> {
  this.statistics.downloads = (this.statistics.downloads || 0) + 1;
  return this.save();
};

UIComponentSchema.methods.incrementViews = function(): Promise<UIComponent> {
  this.statistics.views = (this.statistics.views || 0) + 1;
  return this.save();
};

UIComponentSchema.methods.softDelete = function(userId: Types.ObjectId): Promise<UIComponent> {
  this.deletedAt = new Date();
  this.deletedBy = userId;
  this.isActive = false;
  return this.save();
};

UIComponentSchema.methods.restore = function(): Promise<UIComponent> {
  this.deletedAt = undefined;
  this.deletedBy = undefined;
  this.isActive = true;
  return this.save();
};
