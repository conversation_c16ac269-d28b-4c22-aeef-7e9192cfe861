/**
 * 服务发现服务
 * 
 * 提供服务注册、发现、负载均衡、健康检查和服务路由功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter } from 'events';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 服务状态
 */
export enum ServiceStatus {
  STARTING = 'starting',
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  MAINTENANCE = 'maintenance',
  STOPPING = 'stopping',
  STOPPED = 'stopped'
}

/**
 * 负载均衡策略
 */
export enum LoadBalanceStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  IP_HASH = 'ip_hash',
  LEAST_RESPONSE_TIME = 'least_response_time',
  RANDOM = 'random'
}

/**
 * 服务实例
 */
export interface ServiceInstance {
  instanceId: string;
  serviceId: string;
  serviceName: string;
  version: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'grpc' | 'tcp';
  endpoints: ServiceEndpoint[];
  metadata: Record<string, any>;
  tags: string[];
  weight: number;
  status: ServiceStatus;
  health: InstanceHealth;
  registeredAt: number;
  lastHeartbeat: number;
  ttl: number;
}

/**
 * 服务端点
 */
export interface ServiceEndpoint {
  name: string;
  path: string;
  method: string;
  description: string;
  parameters: EndpointParameter[];
  responses: EndpointResponse[];
  healthCheck?: boolean;
  rateLimit?: RateLimit;
}

/**
 * 端点参数
 */
export interface EndpointParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
  example?: any;
}

/**
 * 端点响应
 */
export interface EndpointResponse {
  status: number;
  description: string;
  schema?: any;
  example?: any;
}

/**
 * 速率限制
 */
export interface RateLimit {
  requests: number;
  window: number; // seconds
  burst?: number;
}

/**
 * 实例健康状态
 */
export interface InstanceHealth {
  status: ServiceStatus;
  checks: HealthCheck[];
  responseTime: number;
  uptime: number;
  lastCheck: number;
  consecutiveFailures: number;
}

/**
 * 健康检查
 */
export interface HealthCheck {
  name: string;
  type: 'http' | 'tcp' | 'grpc' | 'custom';
  endpoint?: string;
  interval: number;
  timeout: number;
  retries: number;
  status: 'passing' | 'warning' | 'critical';
  output?: string;
  lastCheck: number;
}

/**
 * 服务查询
 */
export interface ServiceQuery {
  serviceName?: string;
  version?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  status?: ServiceStatus[];
  region?: string;
  datacenter?: string;
}

/**
 * 负载均衡配置
 */
export interface LoadBalanceConfig {
  strategy: LoadBalanceStrategy;
  healthyOnly: boolean;
  maxRetries: number;
  retryDelay: number;
  circuitBreaker?: CircuitBreakerConfig;
}

/**
 * 熔断器配置
 */
export interface CircuitBreakerConfig {
  enabled: boolean;
  failureThreshold: number;
  recoveryTimeout: number;
  halfOpenMaxCalls: number;
}

/**
 * 服务路由规则
 */
export interface ServiceRoute {
  routeId: string;
  serviceName: string;
  conditions: RouteCondition[];
  targets: RouteTarget[];
  loadBalance: LoadBalanceConfig;
  priority: number;
  enabled: boolean;
}

/**
 * 路由条件
 */
export interface RouteCondition {
  type: 'header' | 'query' | 'path' | 'method' | 'ip' | 'user';
  key: string;
  operator: 'eq' | 'ne' | 'in' | 'not_in' | 'regex' | 'exists';
  value: any;
}

/**
 * 路由目标
 */
export interface RouteTarget {
  instanceId?: string;
  serviceName?: string;
  version?: string;
  weight: number;
  backup?: boolean;
}

/**
 * 服务发现服务
 */
@Injectable()
export class ServiceDiscoveryService {
  private readonly logger = new Logger(ServiceDiscoveryService.name);
  
  // 服务注册表
  private serviceRegistry = new Map<string, ServiceInstance[]>();
  private instanceRegistry = new Map<string, ServiceInstance>();
  
  // 路由规则
  private routingRules = new Map<string, ServiceRoute>();
  
  // 负载均衡状态
  private loadBalanceState = new Map<string, any>();
  
  // 配置
  private config = {
    defaultTTL: 30000, // 30秒
    healthCheckInterval: 15000, // 15秒
    cleanupInterval: 60000, // 1分钟
    maxConsecutiveFailures: 3,
    defaultLoadBalanceStrategy: LoadBalanceStrategy.ROUND_ROBIN
  };

  private readonly eventEmitter = new EventEmitter();

  constructor() {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 启动健康检查
      this.startHealthChecking();
      
      // 启动清理任务
      this.startCleanupTask();
      
      this.logger.log('服务发现服务初始化完成');
      
    } catch (error) {
      this.logger.error('服务发现初始化失败:', error);
    }
  }

  /**
   * 注册服务实例
   */
  public async registerInstance(instance: ServiceInstance): Promise<void> {
    // 验证实例信息
    this.validateInstance(instance);
    
    // 设置默认值
    const registeredInstance: ServiceInstance = {
      ...instance,
      registeredAt: Date.now(),
      lastHeartbeat: Date.now(),
      ttl: instance.ttl || this.config.defaultTTL,
      status: instance.status || ServiceStatus.STARTING
    };
    
    // 添加到注册表
    this.instanceRegistry.set(instance.instanceId, registeredInstance);
    
    // 按服务名分组
    const serviceName = instance.serviceName;
    if (!this.serviceRegistry.has(serviceName)) {
      this.serviceRegistry.set(serviceName, []);
    }
    
    const serviceInstances = this.serviceRegistry.get(serviceName)!;
    const existingIndex = serviceInstances.findIndex(
      inst => inst.instanceId === instance.instanceId
    );
    
    if (existingIndex >= 0) {
      serviceInstances[existingIndex] = registeredInstance;
    } else {
      serviceInstances.push(registeredInstance);
    }
    
    this.logger.log(`服务实例已注册: ${serviceName}/${instance.instanceId}`);
    
    // 发布事件
    this.eventEmitter.emit('service.instance_registered', { instance: registeredInstance });
  }

  /**
   * 注销服务实例
   */
  public async deregisterInstance(instanceId: string): Promise<void> {
    const instance = this.instanceRegistry.get(instanceId);
    if (!instance) {
      throw new Error(`实例不存在: ${instanceId}`);
    }
    
    // 从注册表移除
    this.instanceRegistry.delete(instanceId);
    
    const serviceInstances = this.serviceRegistry.get(instance.serviceName);
    if (serviceInstances) {
      const filteredInstances = serviceInstances.filter(
        inst => inst.instanceId !== instanceId
      );
      
      if (filteredInstances.length === 0) {
        this.serviceRegistry.delete(instance.serviceName);
      } else {
        this.serviceRegistry.set(instance.serviceName, filteredInstances);
      }
    }
    
    this.logger.log(`服务实例已注销: ${instance.serviceName}/${instanceId}`);
    
    // 发布事件
    this.eventEmitter.emit('service.instance_deregistered', { instance });
  }

  /**
   * 发现服务实例
   */
  public async discoverServices(query: ServiceQuery): Promise<ServiceInstance[]> {
    let instances: ServiceInstance[] = [];
    
    // 按服务名查找
    if (query.serviceName) {
      instances = this.serviceRegistry.get(query.serviceName) || [];
    } else {
      // 获取所有实例
      for (const serviceInstances of this.serviceRegistry.values()) {
        instances.push(...serviceInstances);
      }
    }
    
    // 应用过滤条件
    instances = this.applyQueryFilters(instances, query);
    
    return instances;
  }

  /**
   * 获取服务实例（负载均衡）
   */
  public async getServiceInstance(
    serviceName: string,
    strategy: LoadBalanceStrategy = this.config.defaultLoadBalanceStrategy,
    context?: any
  ): Promise<ServiceInstance | null> {
    const instances = await this.discoverServices({
      serviceName,
      status: [ServiceStatus.HEALTHY]
    });
    
    if (instances.length === 0) {
      return null;
    }
    
    return this.selectInstance(instances, strategy, context);
  }

  /**
   * 选择服务实例
   */
  private selectInstance(
    instances: ServiceInstance[],
    strategy: LoadBalanceStrategy,
    context?: any
  ): ServiceInstance {
    switch (strategy) {
      case LoadBalanceStrategy.ROUND_ROBIN:
        return this.roundRobinSelect(instances);
      case LoadBalanceStrategy.LEAST_CONNECTIONS:
        return this.leastConnectionsSelect(instances);
      case LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
        return this.weightedRoundRobinSelect(instances);
      case LoadBalanceStrategy.IP_HASH:
        return this.ipHashSelect(instances, context?.clientIP);
      case LoadBalanceStrategy.LEAST_RESPONSE_TIME:
        return this.leastResponseTimeSelect(instances);
      case LoadBalanceStrategy.RANDOM:
        return this.randomSelect(instances);
      default:
        return instances[0];
    }
  }

  /**
   * 轮询选择
   */
  private roundRobinSelect(instances: ServiceInstance[]): ServiceInstance {
    const serviceName = instances[0].serviceName;
    let state = this.loadBalanceState.get(serviceName) || { index: 0 };
    
    const instance = instances[state.index % instances.length];
    state.index = (state.index + 1) % instances.length;
    
    this.loadBalanceState.set(serviceName, state);
    return instance;
  }

  /**
   * 最少连接选择
   */
  private leastConnectionsSelect(instances: ServiceInstance[]): ServiceInstance {
    // 简化实现：选择权重最高的实例
    return instances.reduce((prev, current) => 
      current.weight > prev.weight ? current : prev
    );
  }

  /**
   * 加权轮询选择
   */
  private weightedRoundRobinSelect(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, inst) => sum + inst.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const instance of instances) {
      currentWeight += instance.weight;
      if (random <= currentWeight) {
        return instance;
      }
    }
    
    return instances[0];
  }

  /**
   * IP哈希选择
   */
  private ipHashSelect(instances: ServiceInstance[], clientIP?: string): ServiceInstance {
    if (!clientIP) {
      return this.randomSelect(instances);
    }
    
    const hash = this.hashString(clientIP);
    const index = hash % instances.length;
    return instances[index];
  }

  /**
   * 最少响应时间选择
   */
  private leastResponseTimeSelect(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((prev, current) => 
      current.health.responseTime < prev.health.responseTime ? current : prev
    );
  }

  /**
   * 随机选择
   */
  private randomSelect(instances: ServiceInstance[]): ServiceInstance {
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  /**
   * 心跳检查
   */
  public async heartbeat(instanceId: string): Promise<void> {
    const instance = this.instanceRegistry.get(instanceId);
    if (!instance) {
      throw new Error(`实例不存在: ${instanceId}`);
    }
    
    instance.lastHeartbeat = Date.now();
    
    // 如果实例之前不健康，现在收到心跳，更新状态
    if (instance.status === ServiceStatus.UNHEALTHY) {
      instance.status = ServiceStatus.HEALTHY;
      instance.health.consecutiveFailures = 0;
      
      this.eventEmitter.emit('service.instance_recovered', { instance });
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthChecking(): void {
    setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheckInterval);
  }

  /**
   * 执行健康检查
   */
  private async performHealthChecks(): Promise<void> {
    for (const instance of this.instanceRegistry.values()) {
      try {
        await this.checkInstanceHealth(instance);
      } catch (error) {
        this.logger.error(`健康检查失败 [${instance.instanceId}]:`, error);
      }
    }
  }

  /**
   * 检查实例健康状态
   */
  private async checkInstanceHealth(instance: ServiceInstance): Promise<void> {
    const now = Date.now();
    
    // 检查心跳超时
    if (now - instance.lastHeartbeat > instance.ttl) {
      instance.health.consecutiveFailures++;
      
      if (instance.health.consecutiveFailures >= this.config.maxConsecutiveFailures) {
        instance.status = ServiceStatus.UNHEALTHY;
        
        this.eventEmitter.emit('service.instance_unhealthy', { instance });
      }
    }
    
    // 执行健康检查
    for (const check of instance.health.checks) {
      await this.executeHealthCheck(instance, check);
    }
    
    instance.health.lastCheck = now;
  }

  /**
   * 执行健康检查
   */
  private async executeHealthCheck(
    instance: ServiceInstance,
    check: HealthCheck
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // 这里应该实际执行健康检查
      // 暂时使用模拟实现
      await this.simulateHealthCheck(instance, check);
      
      check.status = 'passing';
      check.output = '健康检查通过';
      
    } catch (error) {
      check.status = 'critical';
      check.output = error.message;
      
      instance.health.consecutiveFailures++;
    } finally {
      check.lastCheck = Date.now();
      instance.health.responseTime = Date.now() - startTime;
    }
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    setInterval(async () => {
      await this.cleanupExpiredInstances();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理过期实例
   */
  private async cleanupExpiredInstances(): Promise<void> {
    const now = Date.now();
    const expiredInstances: string[] = [];
    
    for (const [instanceId, instance] of this.instanceRegistry.entries()) {
      // 检查实例是否过期
      if (now - instance.lastHeartbeat > instance.ttl * 2) {
        expiredInstances.push(instanceId);
      }
    }
    
    // 移除过期实例
    for (const instanceId of expiredInstances) {
      await this.deregisterInstance(instanceId);
      this.logger.log(`清理过期实例: ${instanceId}`);
    }
  }

  // 辅助方法
  private validateInstance(instance: ServiceInstance): void {
    if (!instance.instanceId || !instance.serviceName || !instance.host || !instance.port) {
      throw new Error('实例信息不完整');
    }
  }

  private applyQueryFilters(instances: ServiceInstance[], query: ServiceQuery): ServiceInstance[] {
    let filtered = instances;
    
    if (query.version) {
      filtered = filtered.filter(inst => inst.version === query.version);
    }
    
    if (query.tags && query.tags.length > 0) {
      filtered = filtered.filter(inst => 
        query.tags!.every(tag => inst.tags.includes(tag))
      );
    }
    
    if (query.status && query.status.length > 0) {
      filtered = filtered.filter(inst => query.status!.includes(inst.status));
    }
    
    if (query.metadata) {
      filtered = filtered.filter(inst => {
        for (const [key, value] of Object.entries(query.metadata!)) {
          if (inst.metadata[key] !== value) {
            return false;
          }
        }
        return true;
      });
    }
    
    return filtered;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }

  private async simulateHealthCheck(instance: ServiceInstance, check: HealthCheck): Promise<void> {
    // 模拟健康检查
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    
    // 模拟偶尔的失败
    if (Math.random() < 0.05) {
      throw new Error('健康检查失败');
    }
  }

  /**
   * 获取服务列表
   */
  public getServiceList(): string[] {
    return Array.from(this.serviceRegistry.keys());
  }

  /**
   * 获取服务实例列表
   */
  public getServiceInstances(serviceName: string): ServiceInstance[] {
    return this.serviceRegistry.get(serviceName) || [];
  }

  /**
   * 获取实例详情
   */
  public getInstance(instanceId: string): ServiceInstance | undefined {
    return this.instanceRegistry.get(instanceId);
  }

  /**
   * 定期清理
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async periodicCleanup(): Promise<void> {
    await this.cleanupExpiredInstances();
    this.logger.log('定期清理完成');
  }
}
