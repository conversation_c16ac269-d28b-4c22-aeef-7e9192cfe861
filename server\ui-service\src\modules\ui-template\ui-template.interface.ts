/**
 * 模板类型枚举
 */
export enum TemplateType {
  PAGE = 'page',
  LAYOUT = 'layout',
  SECTION = 'section',
  WIDGET = 'widget',
  FORM = 'form',
  DASHBOARD = 'dashboard',
  LANDING = 'landing',
  ADMIN = 'admin'
}

/**
 * 模板状态枚举
 */
export enum TemplateStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated'
}

/**
 * 模板分类枚举
 */
export enum TemplateCategory {
  BUSINESS = 'business',
  ECOMMERCE = 'ecommerce',
  PORTFOLIO = 'portfolio',
  BLOG = 'blog',
  EDUCATION = 'education',
  HEALTHCARE = 'healthcare',
  FINANCE = 'finance',
  ENTERTAINMENT = 'entertainment',
  GOVERNMENT = 'government',
  NONPROFIT = 'nonprofit'
}

/**
 * 响应式断点接口
 */
export interface ResponsiveBreakpoint {
  name: string;
  minWidth: number;
  maxWidth?: number;
  columns: number;
  gutter: number;
}

/**
 * 模板布局接口
 */
export interface TemplateLayout {
  type: 'grid' | 'flexbox' | 'absolute' | 'flow';
  columns?: number;
  rows?: number;
  gap?: number;
  padding?: number;
  responsive: ResponsiveBreakpoint[];
}

/**
 * 模板组件引用接口
 */
export interface TemplateComponentRef {
  id: string;
  componentId: string;
  componentName: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  props: Record<string, any>;
  style: Record<string, any>;
  responsive?: {
    [breakpoint: string]: {
      position?: Partial<TemplateComponentRef['position']>;
      props?: Record<string, any>;
      style?: Record<string, any>;
      visible?: boolean;
    };
  };
}

/**
 * 模板数据源接口
 */
export interface TemplateDataSource {
  id: string;
  name: string;
  type: 'static' | 'api' | 'database' | 'file';
  config: {
    url?: string;
    method?: string;
    headers?: Record<string, string>;
    query?: Record<string, any>;
    transform?: string; // JavaScript代码
  };
  schema: Record<string, any>; // JSON Schema
  mockData?: any;
}

/**
 * 模板变量接口
 */
export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  defaultValue: any;
  description?: string;
  required?: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
}

/**
 * 模板配置接口
 */
export interface TemplateConfig {
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
    ogImage?: string;
  };
  performance?: {
    lazyLoading?: boolean;
    caching?: boolean;
    compression?: boolean;
    minification?: boolean;
  };
  accessibility?: {
    ariaLabels?: boolean;
    keyboardNavigation?: boolean;
    screenReader?: boolean;
    highContrast?: boolean;
  };
  analytics?: {
    googleAnalytics?: string;
    facebookPixel?: string;
    customEvents?: string[];
  };
}

/**
 * UI模板接口
 */
export interface UITemplate {
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  category: TemplateCategory;
  status: TemplateStatus;
  version: string;
  
  // 模板结构
  layout: TemplateLayout;
  components: TemplateComponentRef[];
  dataSources: TemplateDataSource[];
  variables: TemplateVariable[];
  
  // 样式和主题
  themeId?: string;
  customCSS?: string;
  customJS?: string;
  
  // 配置
  config: TemplateConfig;
  
  // 预览
  thumbnailUrl?: string;
  previewUrl?: string;
  demoUrl?: string;
  
  // 元数据
  tags: string[];
  isPublic: boolean;
  isResponsive: boolean;
  supportedDevices: string[];
  requiredComponents: string[];
  
  // 统计
  statistics: {
    views: number;
    downloads: number;
    likes: number;
    uses: number;
    rating: number;
    reviews: number;
  };
  
  // 审计字段
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  publishedBy?: string;
}

/**
 * 模板构建结果接口
 */
export interface TemplateBuildResult {
  templateId: string;
  html: string;
  css: string;
  js: string;
  assets: string[];
  metadata: {
    buildTime: number;
    size: number;
    dependencies: string[];
  };
}

/**
 * 模板部署配置接口
 */
export interface TemplateDeployConfig {
  platform: 'static' | 'cdn' | 'server' | 'cloud';
  domain?: string;
  subdomain?: string;
  customDomain?: string;
  ssl?: boolean;
  caching?: {
    enabled: boolean;
    ttl: number;
    strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
  };
  environment: Record<string, string>;
}

/**
 * 模板使用统计接口
 */
export interface TemplateUsageStats {
  templateId: string;
  totalUses: number;
  activeUses: number;
  popularVariations: {
    variation: string;
    count: number;
  }[];
  deviceBreakdown: {
    device: string;
    percentage: number;
  }[];
  performanceMetrics: {
    averageLoadTime: number;
    averageRenderTime: number;
    errorRate: number;
  };
  userFeedback: {
    averageRating: number;
    totalReviews: number;
    commonIssues: string[];
  };
}
