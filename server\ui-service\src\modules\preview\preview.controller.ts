import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
  Logger
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { PreviewService } from './preview.service';
import {
  PreviewConfig,
  PreviewType,
  DeviceType,
  PreviewSession
} from './preview.interface';

@ApiTags('实时预览')
@Controller('preview')
export class PreviewController {
  private readonly logger = new Logger(PreviewController.name);

  constructor(private readonly previewService: PreviewService) {}

  /**
   * 创建预览会话
   */
  @Post('sessions')
  @ApiOperation({ summary: '创建预览会话' })
  @ApiResponse({ status: 201, description: '预览会话创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiBearerAuth()
  async createSession(
    @Body() config: PreviewConfig,
    @Request() req: any
  ): Promise<{
    success: boolean;
    data: PreviewSession;
    message: string;
  }> {
    try {
      const userId = req.user?.id || 'anonymous';
      const session = await this.previewService.createPreviewSession(config, userId);
      
      return {
        success: true,
        data: session,
        message: '预览会话创建成功'
      };
    } catch (error) {
      this.logger.error(`创建预览会话失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取预览会话
   */
  @Get('sessions/:sessionId')
  @ApiOperation({ summary: '获取预览会话' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getSession(@Param('sessionId') sessionId: string): Promise<{
    success: boolean;
    data: PreviewSession;
  }> {
    try {
      const session = await this.previewService.getPreviewSession(sessionId);
      
      return {
        success: true,
        data: session
      };
    } catch (error) {
      this.logger.error(`获取预览会话失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新预览配置
   */
  @Put('sessions/:sessionId/config')
  @ApiOperation({ summary: '更新预览配置' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async updateConfig(
    @Param('sessionId') sessionId: string,
    @Body() config: Partial<PreviewConfig>
  ): Promise<{
    success: boolean;
    data: PreviewSession;
    message: string;
  }> {
    try {
      const session = await this.previewService.updatePreviewConfig(sessionId, config);
      
      return {
        success: true,
        data: session,
        message: '预览配置更新成功'
      };
    } catch (error) {
      this.logger.error(`更新预览配置失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除预览会话
   */
  @Delete('sessions/:sessionId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除预览会话' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 204, description: '删除成功' })
  async deleteSession(@Param('sessionId') sessionId: string): Promise<void> {
    try {
      await this.previewService.deletePreviewSession(sessionId);
      this.logger.log(`删除预览会话: ${sessionId}`);
    } catch (error) {
      this.logger.error(`删除预览会话失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建预览快照
   */
  @Post('sessions/:sessionId/snapshots')
  @ApiOperation({ summary: '创建预览快照' })
  @ApiParam({ name: 'sessionId', description: '会话ID' })
  @ApiResponse({ status: 201, description: '快照创建成功' })
  @ApiResponse({ status: 400, description: '预览未就绪' })
  async createSnapshot(@Param('sessionId') sessionId: string): Promise<{
    success: boolean;
    data: any;
    message: string;
  }> {
    try {
      const snapshot = await this.previewService.createSnapshot(sessionId);
      
      return {
        success: true,
        data: snapshot,
        message: '预览快照创建成功'
      };
    } catch (error) {
      this.logger.error(`创建预览快照失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取用户预览会话列表
   */
  @Get('sessions')
  @ApiOperation({ summary: '获取用户预览会话列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiBearerAuth()
  async getUserSessions(@Request() req: any): Promise<{
    success: boolean;
    data: PreviewSession[];
  }> {
    try {
      const userId = req.user?.id || 'anonymous';
      const sessions = await this.previewService.getUserPreviewSessions(userId);
      
      return {
        success: true,
        data: sessions
      };
    } catch (error) {
      this.logger.error(`获取用户预览会话失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取预览统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取预览统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getStats(): Promise<{
    success: boolean;
    data: any;
  }> {
    try {
      const stats = await this.previewService.getPreviewStats();
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      this.logger.error(`获取预览统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 清理过期会话
   */
  @Post('cleanup')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '清理过期会话' })
  @ApiResponse({ status: 204, description: '清理完成' })
  async cleanup(): Promise<void> {
    try {
      await this.previewService.cleanupExpiredSessions();
      this.logger.log('清理过期预览会话完成');
    } catch (error) {
      this.logger.error(`清理过期会话失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 预览特定资源
   */
  @Get(':type/:resourceId')
  @ApiOperation({ summary: '预览特定资源' })
  @ApiParam({ name: 'type', enum: PreviewType, description: '预览类型' })
  @ApiParam({ name: 'resourceId', description: '资源ID' })
  @ApiQuery({ name: 'device', enum: DeviceType, required: false, description: '设备类型' })
  @ApiQuery({ name: 'width', type: Number, required: false, description: '视口宽度' })
  @ApiQuery({ name: 'height', type: Number, required: false, description: '视口高度' })
  @ApiResponse({ status: 200, description: '预览创建成功' })
  async previewResource(
    @Param('type') type: PreviewType,
    @Param('resourceId') resourceId: string,
    @Query('device') device?: DeviceType,
    @Query('width') width?: number,
    @Query('height') height?: number,
    @Request() req?: any
  ): Promise<{
    success: boolean;
    data: PreviewSession;
  }> {
    try {
      const userId = req?.user?.id || 'anonymous';
      
      const config: PreviewConfig = {
        type,
        resourceId,
        viewport: {
          width: width || 1200,
          height: height || 800
        },
        device: device || DeviceType.DESKTOP,
        interactive: true
      };

      const session = await this.previewService.createPreviewSession(config, userId);
      
      return {
        success: true,
        data: session
      };
    } catch (error) {
      this.logger.error(`预览资源失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
