/**
 * 预览类型枚举
 */
export enum PreviewType {
  COMPONENT = 'component',
  TEMPLATE = 'template',
  THEME = 'theme',
  PAGE = 'page'
}

/**
 * 预览状态枚举
 */
export enum PreviewStatus {
  GENERATING = 'generating',
  READY = 'ready',
  ERROR = 'error',
  EXPIRED = 'expired'
}

/**
 * 设备类型枚举
 */
export enum DeviceType {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  MOBILE = 'mobile',
  WATCH = 'watch'
}

/**
 * 预览配置接口
 */
export interface PreviewConfig {
  type: PreviewType;
  resourceId: string;
  viewport: {
    width: number;
    height: number;
    devicePixelRatio?: number;
  };
  device: DeviceType;
  theme?: string;
  locale?: string;
  darkMode?: boolean;
  interactive?: boolean;
  showGrid?: boolean;
  showRulers?: boolean;
  zoom?: number;
}

/**
 * 预览会话接口
 */
export interface PreviewSession {
  id: string;
  userId: string;
  config: PreviewConfig;
  status: PreviewStatus;
  url: string;
  thumbnailUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  metadata: Record<string, any>;
}

/**
 * 预览更新事件接口
 */
export interface PreviewUpdateEvent {
  sessionId: string;
  type: 'config' | 'content' | 'status';
  data: any;
  timestamp: Date;
}

/**
 * 预览快照接口
 */
export interface PreviewSnapshot {
  id: string;
  sessionId: string;
  imageUrl: string;
  config: PreviewConfig;
  createdAt: Date;
  metadata: Record<string, any>;
}

/**
 * 预览统计接口
 */
export interface PreviewStats {
  totalSessions: number;
  activeSessions: number;
  totalSnapshots: number;
  popularDevices: { device: DeviceType; count: number }[];
  popularViewports: { viewport: string; count: number }[];
  averageSessionDuration: number;
  lastUpdated: Date;
}
