{"name": "ui-service", "version": "1.0.0", "description": "DL引擎UI界面管理服务 - 提供UI模板、主题、组件管理等功能", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": ["ui", "template", "theme", "component", "<PERSON><PERSON><PERSON>", "microservice", "dl-engine"], "author": "DL Engine Team", "license": "MIT", "dependencies": {"@nestjs-modules/ioredis": "^1.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.0.0", "@nestjs/websockets": "^10.0.0", "archiver": "^6.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "ioredis": "^5.3.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0", "multer": "^1.4.5-lts.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "sharp": "^0.32.0", "socket.io": "^4.7.0", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/archiver": "^6.0.0", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.0", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/passport": "^1.0.12", "@types/passport-jwt": "^3.0.9", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}