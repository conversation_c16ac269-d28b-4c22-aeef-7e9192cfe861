import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务导入
import { IntelligentSchedulerService } from './intelligent-scheduler.service';
import { PredictiveCacheService } from './predictive-cache.service';
import { AdaptiveNetworkService } from './adaptive-network.service';
import { EdgeCacheOptimizerService } from './services/edge-cache-optimizer.service';
import { IntelligentPreloaderService } from './services/intelligent-preloader.service';
import { NetworkOptimizerService } from './services/network-optimizer.service';
import { PerformanceMonitorService } from './services/performance-monitor.service';

// 控制器导入
import { EdgeEnhancementController } from './edge-enhancement.controller';

/**
 * 边缘计算增强模块 (第二阶段任务9完成)
 *
 * 提供以下功能：
 * - 智能调度、预测性缓存和自适应网络传输功能 (原有)
 * - 边缘缓存优化：智能缓存策略、动态缓存调整、多层级缓存协调 (新增)
 * - 智能预加载：基于ML和用户行为分析的资源预加载 (新增)
 * - 网络优化：智能路由、带宽管理、延迟优化、自适应传输 (新增)
 * - 性能监控：全面的性能监控、指标收集、分析和告警 (新增)
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),
  ],
  
  controllers: [
    EdgeEnhancementController,
  ],
  
  providers: [
    IntelligentSchedulerService,
    PredictiveCacheService,
    AdaptiveNetworkService,
    EdgeCacheOptimizerService,
    IntelligentPreloaderService,
    NetworkOptimizerService,
    PerformanceMonitorService,
    {
      provide: 'REDIS_CLIENT',
      useFactory: () => {
        const Redis = require('ioredis');
        return new Redis({
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          db: parseInt(process.env.REDIS_DB || '0'),
        });
      },
    },
  ],

  exports: [
    IntelligentSchedulerService,
    PredictiveCacheService,
    AdaptiveNetworkService,
    EdgeCacheOptimizerService,
    IntelligentPreloaderService,
    NetworkOptimizerService,
    PerformanceMonitorService,
  ],
})
export class EdgeEnhancementModule {}
