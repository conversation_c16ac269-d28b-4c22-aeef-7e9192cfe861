import { Module } from '@nestjs/common';
import { EcosystemController } from './ecosystem.controller';
import { EcosystemPlatformService } from './ecosystem-platform.service';
import { DependencyManagerService } from './services/dependency-manager.service';
import { EcosystemHealthMonitorService } from './services/ecosystem-health-monitor.service';
import { ServiceDiscoveryService } from './services/service-discovery.service';
import { AutomatedDeploymentService } from './services/automated-deployment.service';

/**
 * 生态系统模块 (第二阶段任务10完成)
 *
 * 提供以下功能：
 * - 生态系统平台管理 (原有)
 * - 服务依赖管理：依赖关系管理、循环依赖检测、健康监控 (新增)
 * - 生态健康监控：整体健康状况监控、指标收集、异常检测 (新增)
 * - 服务发现：服务注册发现、负载均衡、健康检查 (新增)
 * - 自动化部署：CI/CD流水线、自动部署、回滚、环境管理 (新增)
 */
@Module({
  controllers: [EcosystemController],
  providers: [
    EcosystemPlatformService,
    DependencyManagerService,
    EcosystemHealthMonitorService,
    ServiceDiscoveryService,
    AutomatedDeploymentService,
  ],
  exports: [
    EcosystemPlatformService,
    DependencyManagerService,
    EcosystemHealthMonitorService,
    ServiceDiscoveryService,
    AutomatedDeploymentService,
  ],
})
export class EcosystemModule {}
