/**
 * 分布式行为决策服务应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { DistributedBehaviorService } from './services/distributed-behavior.service';
import { MLDecisionService } from './services/ml-decision.service';
import { ABTestService } from './services/ab-test.service';
import { BehaviorController } from './controllers/behavior.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
  ],
  controllers: [BehaviorController],
  providers: [
    DistributedBehaviorService,
    MLDecisionService,
    ABTestService,
    {
      provide: 'REDIS_CONFIG',
      useFactory: () => ({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
      }),
    },
  ],
  exports: [DistributedBehaviorService, MLDecisionService, ABTestService],
})
export class AppModule {}
