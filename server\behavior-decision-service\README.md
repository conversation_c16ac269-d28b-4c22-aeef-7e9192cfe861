# 分布式行为决策服务

## 🚀 第二阶段任务8完成 - 行为决策服务增强

基于NestJS的分布式行为决策服务，提供智能决策、群体协调和分布式计算能力。

### ✅ 新增功能特性

#### 🤖 机器学习决策模型
- **决策树模型**: 基于规则的快速决策
- **随机森林**: 集成学习提高准确率
- **神经网络**: 深度学习复杂模式识别
- **强化学习**: 基于反馈的自适应决策
- **模型自动训练**: 基于历史数据持续优化

#### 🧪 A/B测试框架
- **多变体测试**: 支持2个以上变体对比
- **流量分配**: 灵活的流量分配策略
- **实时统计**: 实时转化率和置信区间计算
- **统计显著性**: 自动检测统计显著性
- **提前停止**: 智能提前停止机制

#### 📊 高级决策算法优化
- **智能负载均衡**: ML优化的负载分配
- **自适应策略**: 基于性能自动调整策略
- **多目标优化**: 同时优化多个业务指标
- **上下文感知**: 基于环境上下文的决策

#### 🔍 完善分布式决策
- **性能监控增强**: 准确率、响应时间、用户满意度
- **趋势分析**: 性能趋势预测和告警
- **模型性能**: ML模型准确率和置信度监控
- **分布式协调优化**: 更智能的节点协调机制

## 主要功能

- **分布式决策**: 支持多节点协同决策 (已优化)
- **机器学习集成**: ML模型驱动的智能决策 (新增)
- **A/B测试框架**: 决策策略效果验证 (新增)
- **负载均衡**: 智能分配决策请求到最优节点 (已增强)
- **故障恢复**: 自动检测和处理节点故障
- **实时监控**: 提供节点状态和性能监控 (已增强)
- **策略配置**: 支持多种协调策略 (已扩展)

## 技术架构

- **框架**: NestJS
- **消息队列**: Redis Pub/Sub
- **缓存**: Redis
- **调度**: @nestjs/schedule
- **事件**: @nestjs/event-emitter

## 安装和运行

### 安装依赖
```bash
npm install
```

### 配置环境变量
复制 `.env` 文件并根据需要修改配置。

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## API 接口

### 提交决策请求
```
POST /behavior/decision
```

### 获取节点状态
```
GET /behavior/status
```

### 获取所有节点状态
```
GET /behavior/nodes
```

### 设置协调策略
```
POST /behavior/strategy/:strategy
```

### 健康检查
```
GET /behavior/health
```

## 协调策略

- `round_robin`: 轮询分配
- `least_load`: 最小负载优先
- `geographic`: 地理位置优先
- `capability_based`: 能力匹配优先

## 监控指标

- 节点负载
- 处理请求数
- 平均响应时间
- 错误率
- 心跳状态

## 故障处理

服务具备以下故障处理能力：

1. **节点故障检测**: 通过心跳机制检测节点状态
2. **自动故障转移**: 将请求重新路由到健康节点
3. **负载重平衡**: 动态调整负载分配
4. **数据一致性**: 确保决策结果的一致性

## 配置说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| PORT | 服务端口 | 3008 |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| NODE_ID | 节点标识 | behavior-node-1 |
| MAX_CAPACITY | 最大处理能力 | 100 |
