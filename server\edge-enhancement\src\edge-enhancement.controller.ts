import { Controller, Get, Post, Put, Delete, Body, Param, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';

import { IntelligentSchedulerService, LoadPrediction, DistributionPlan } from './intelligent-scheduler.service';
import { PredictiveCacheService } from './predictive-cache.service';
import { AdaptiveNetworkService, NetworkQuality, ReliabilityLevel } from './adaptive-network.service';
import { EdgeCacheOptimizerService } from './services/edge-cache-optimizer.service';
import { IntelligentPreloaderService } from './services/intelligent-preloader.service';
import { NetworkOptimizerService, RoutingStrategy } from './services/network-optimizer.service';
import { PerformanceMonitorService, AlertRule } from './services/performance-monitor.service';

/**
 * 边缘计算增强控制器
 * 提供智能调度、预测性缓存和自适应网络传输的API接口
 */
@ApiTags('边缘计算增强')
@Controller('edge-enhancement')
export class EdgeEnhancementController {
  private readonly logger = new Logger(EdgeEnhancementController.name);

  constructor(
    private readonly schedulerService: IntelligentSchedulerService,
    private readonly cacheService: PredictiveCacheService,
    private readonly networkService: AdaptiveNetworkService,
    private readonly cacheOptimizerService: EdgeCacheOptimizerService,
    private readonly preloaderService: IntelligentPreloaderService,
    private readonly networkOptimizerService: NetworkOptimizerService,
    private readonly performanceMonitorService: PerformanceMonitorService,
  ) {}

  // ==================== 智能调度相关接口 ====================

  @Get('scheduler/predict/:nodeId')
  @ApiOperation({ summary: '预测节点负载' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiQuery({ name: 'timeWindow', description: '预测时间窗口（分钟）', required: false })
  @ApiResponse({ status: 200, description: '负载预测结果' })
  async predictNodeLoad(
    @Param('nodeId') nodeId: string,
    @Query('timeWindow') timeWindow?: number
  ): Promise<LoadPrediction> {
    this.logger.log(`预测节点负载: ${nodeId}, 时间窗口: ${timeWindow || 30}分钟`);
    return await this.schedulerService.predictLoad(nodeId, timeWindow);
  }

  @Post('scheduler/distribute')
  @ApiOperation({ summary: '智能流量分配' })
  @ApiResponse({ status: 200, description: '流量分配计划' })
  async distributeTraffic(
    @Body() body: {
      requests: any[];
      availableNodes: any[];
    }
  ): Promise<DistributionPlan> {
    this.logger.log(`智能流量分配: ${body.requests.length} 个请求, ${body.availableNodes.length} 个节点`);
    return await this.schedulerService.distributeTraffic(body.requests, body.availableNodes);
  }

  @Post('scheduler/load-data/:nodeId')
  @ApiOperation({ summary: '添加负载数据' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '负载数据已添加' })
  addLoadData(
    @Param('nodeId') nodeId: string,
    @Body() loadData: any
  ): { success: boolean } {
    this.schedulerService.addLoadData(nodeId, loadData);
    this.logger.log(`添加负载数据: ${nodeId}`);
    return { success: true };
  }

  @Get('scheduler/statistics')
  @ApiOperation({ summary: '获取调度统计信息' })
  @ApiResponse({ status: 200, description: '调度统计信息' })
  getSchedulerStatistics(): any {
    return this.schedulerService.getStatistics();
  }

  @Post('scheduler/reset')
  @ApiOperation({ summary: '重置学习参数' })
  @ApiResponse({ status: 200, description: '学习参数已重置' })
  resetSchedulerLearning(): { success: boolean } {
    this.schedulerService.resetLearning();
    this.logger.log('调度器学习参数已重置');
    return { success: true };
  }

  // ==================== 预测性缓存相关接口 ====================

  @Post('cache/analyze/:userId')
  @ApiOperation({ summary: '分析用户行为并预加载' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiResponse({ status: 200, description: '行为分析完成' })
  async analyzeBehaviorAndPreload(
    @Param('userId') userId: string
  ): Promise<{ success: boolean }> {
    await this.cacheService.analyzeBehaviorAndPreload(userId);
    this.logger.log(`用户行为分析完成: ${userId}`);
    return { success: true };
  }

  @Get('cache/:key')
  @ApiOperation({ summary: '获取缓存项' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存值' })
  async getCacheItem(@Param('key') key: string): Promise<any> {
    const value = await this.cacheService.get(key);
    return { key, value, found: value !== null };
  }

  @Post('cache/:key')
  @ApiOperation({ summary: '设置缓存项' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存项已设置' })
  async setCacheItem(
    @Param('key') key: string,
    @Body() body: {
      value: any;
      options?: any;
    }
  ): Promise<{ success: boolean }> {
    await this.cacheService.set(key, body.value, body.options);
    this.logger.log(`设置缓存项: ${key}`);
    return { success: true };
  }

  @Delete('cache/:key')
  @ApiOperation({ summary: '删除缓存项' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存项删除结果' })
  async deleteCacheItem(@Param('key') key: string): Promise<{ success: boolean; deleted: boolean }> {
    const deleted = await this.cacheService.delete(key);
    this.logger.log(`删除缓存项: ${key}, 结果: ${deleted}`);
    return { success: true, deleted };
  }

  @Get('cache/exists/:key')
  @ApiOperation({ summary: '检查缓存是否存在' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存存在性检查结果' })
  async checkCacheExists(@Param('key') key: string): Promise<{ exists: boolean }> {
    const exists = await this.cacheService.has(key);
    return { exists };
  }

  @Delete('cache')
  @ApiOperation({ summary: '清空所有缓存' })
  @ApiResponse({ status: 200, description: '所有缓存已清空' })
  async clearAllCache(): Promise<{ success: boolean }> {
    await this.cacheService.clear();
    this.logger.log('所有缓存已清空');
    return { success: true };
  }

  @Get('cache/statistics')
  @ApiOperation({ summary: '获取缓存统计信息' })
  @ApiResponse({ status: 200, description: '缓存统计信息' })
  getCacheStatistics(): any {
    return this.cacheService.getStatistics();
  }

  // ==================== 自适应网络相关接口 ====================

  @Post('network/encode')
  @ApiOperation({ summary: '自适应编码数据' })
  @ApiResponse({ status: 200, description: '编码后的数据' })
  async encodeData(
    @Body() body: {
      data: any;
      targetNode: any;
    }
  ): Promise<any> {
    const encodedData = await this.networkService.encodeData(body.data, body.targetNode);
    this.logger.log(`数据编码完成, 压缩比: ${encodedData.metadata.compressionRatio.toFixed(3)}`);
    return encodedData;
  }

  @Post('network/send-reliable')
  @ApiOperation({ summary: '可靠传输数据' })
  @ApiResponse({ status: 200, description: '传输结果' })
  async sendWithReliability(
    @Body() body: {
      data: string; // Base64编码的数据
      destination: any;
      reliabilityLevel: ReliabilityLevel;
    }
  ): Promise<any> {
    const dataBuffer = Buffer.from(body.data, 'base64');
    const result = await this.networkService.sendWithReliability(
      dataBuffer,
      body.destination,
      body.reliabilityLevel
    );
    this.logger.log(`可靠传输完成, 成功: ${result.success}, 延迟: ${result.actualLatency}ms`);
    return result;
  }

  @Put('network/quality/:nodeId')
  @ApiOperation({ summary: '更新网络质量' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '网络质量已更新' })
  updateNetworkQuality(
    @Param('nodeId') nodeId: string,
    @Body() quality: Partial<NetworkQuality>
  ): { success: boolean } {
    this.networkService.updateNetworkQuality(nodeId, quality);
    this.logger.log(`更新网络质量: ${nodeId}`);
    return { success: true };
  }

  @Get('network/statistics')
  @ApiOperation({ summary: '获取传输统计信息' })
  @ApiResponse({ status: 200, description: '传输统计信息' })
  getNetworkStatistics(): any {
    return this.networkService.getTransmissionStatistics();
  }

  @Post('network/reset-statistics')
  @ApiOperation({ summary: '重置传输统计信息' })
  @ApiResponse({ status: 200, description: '传输统计信息已重置' })
  resetNetworkStatistics(): { success: boolean } {
    this.networkService.resetStatistics();
    this.logger.log('传输统计信息已重置');
    return { success: true };
  }

  // ==================== 综合状态接口 ====================

  @Get('status')
  @ApiOperation({ summary: '获取边缘计算增强服务状态' })
  @ApiResponse({ status: 200, description: '服务状态信息' })
  getServiceStatus(): any {
    return {
      timestamp: new Date().toISOString(),
      services: {
        scheduler: {
          status: 'running',
          statistics: this.schedulerService.getStatistics()
        },
        cache: {
          status: 'running',
          statistics: this.cacheService.getStatistics()
        },
        network: {
          status: 'running',
          statistics: this.networkService.getTransmissionStatistics()
        }
      },
      version: '1.0.0',
      uptime: process.uptime()
    };
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '服务健康状态' })
  healthCheck(): { status: string; timestamp: string } {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };
  }

  // ==================== 配置管理接口 ====================

  @Get('config')
  @ApiOperation({ summary: '获取服务配置' })
  @ApiResponse({ status: 200, description: '服务配置信息' })
  getServiceConfig(): any {
    return {
      scheduler: {
        learningRate: 0.1,
        discountFactor: 0.9,
        explorationRate: 0.1
      },
      cache: {
        l1MaxSize: 1000,
        l2MaxSize: 10000,
        l3MaxSize: 100000,
        defaultTTL: 3600,
        preloadThreshold: 0.7
      },
      network: {
        enableCompression: true,
        enableFEC: true,
        defaultReliability: ReliabilityLevel.RELIABLE
      }
    };
  }

  @Put('config')
  @ApiOperation({ summary: '更新服务配置' })
  @ApiResponse({ status: 200, description: '配置已更新' })
  updateServiceConfig(
    @Body() config: any
  ): { success: boolean; message: string } {
    // 这里应该实现配置更新逻辑
    this.logger.log('服务配置更新请求', config);
    return {
      success: true,
      message: '配置更新功能待实现'
    };
  }

  // ==================== 边缘缓存优化接口 (新增) ====================

  @Get('cache-optimizer/metrics')
  @ApiOperation({ summary: '获取缓存优化性能指标' })
  @ApiResponse({ status: 200, description: '缓存性能指标' })
  async getCacheOptimizerMetrics() {
    this.logger.log('获取缓存优化性能指标');
    return this.cacheOptimizerService.getPerformanceMetrics();
  }

  @Get('cache-optimizer/statistics')
  @ApiOperation({ summary: '获取缓存优化统计信息' })
  @ApiResponse({ status: 200, description: '缓存统计信息' })
  async getCacheOptimizerStatistics() {
    this.logger.log('获取缓存优化统计信息');
    return this.cacheOptimizerService.getStatistics();
  }

  @Get('cache-optimizer/:key')
  @ApiOperation({ summary: '智能缓存获取' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存数据' })
  async getFromCache(@Param('key') key: string) {
    this.logger.log(`智能缓存获取: ${key}`);
    return await this.cacheOptimizerService.get(key);
  }

  @Post('cache-optimizer/:key')
  @ApiOperation({ summary: '智能缓存设置' })
  @ApiParam({ name: 'key', description: '缓存键' })
  @ApiResponse({ status: 200, description: '缓存设置成功' })
  async setToCache(
    @Param('key') key: string,
    @Body() body: { value: any; options?: any }
  ) {
    this.logger.log(`智能缓存设置: ${key}`);
    await this.cacheOptimizerService.set(key, body.value, body.options);
    return { success: true, message: '缓存设置成功' };
  }

  // ==================== 智能预加载接口 (新增) ====================

  @Post('preloader/analyze')
  @ApiOperation({ summary: '分析用户行为并生成预加载任务' })
  @ApiResponse({ status: 200, description: '预加载任务列表' })
  async analyzeAndPreload(
    @Body() body: {
      userId: string;
      sessionId: string;
      currentResource: string;
      context: any;
    }
  ) {
    this.logger.log(`分析用户行为: ${body.userId}`);
    const tasks = await this.preloaderService.analyzeAndPreload(
      body.userId,
      body.sessionId,
      body.currentResource,
      body.context
    );
    return { success: true, tasks };
  }

  @Get('preloader/metrics')
  @ApiOperation({ summary: '获取预加载性能指标' })
  @ApiResponse({ status: 200, description: '预加载性能指标' })
  async getPreloaderMetrics() {
    this.logger.log('获取预加载性能指标');
    return this.preloaderService.getMetrics();
  }

  @Get('preloader/statistics')
  @ApiOperation({ summary: '获取预加载统计信息' })
  @ApiResponse({ status: 200, description: '预加载统计信息' })
  async getPreloaderStatistics() {
    this.logger.log('获取预加载统计信息');
    return this.preloaderService.getStatistics();
  }

  // ==================== 网络优化接口 (新增) ====================

  @Post('network-optimizer/route')
  @ApiOperation({ summary: '查找最优路由' })
  @ApiResponse({ status: 200, description: '最优路由路径' })
  async findOptimalRoute(
    @Body() body: {
      sourceNode: string;
      targetNode: string;
      strategy?: RoutingStrategy;
      constraints?: any;
    }
  ) {
    this.logger.log(`查找最优路由: ${body.sourceNode} -> ${body.targetNode}`);
    const route = await this.networkOptimizerService.findOptimalRoute(
      body.sourceNode,
      body.targetNode,
      body.strategy,
      body.constraints
    );
    return { success: true, route };
  }

  @Post('network-optimizer/transmission')
  @ApiOperation({ summary: '创建传输任务' })
  @ApiResponse({ status: 200, description: '传输任务' })
  async createTransmissionTask(
    @Body() body: {
      sourceNode: string;
      targetNode: string;
      dataSize: number;
      priority?: number;
      deadline?: number;
      config?: any;
    }
  ) {
    this.logger.log(`创建传输任务: ${body.sourceNode} -> ${body.targetNode}`);
    const task = await this.networkOptimizerService.createTransmissionTask(
      body.sourceNode,
      body.targetNode,
      body.dataSize,
      body.priority,
      body.deadline,
      body.config
    );
    return { success: true, task };
  }

  @Get('network-optimizer/metrics')
  @ApiOperation({ summary: '获取网络优化指标' })
  @ApiResponse({ status: 200, description: '网络优化指标' })
  async getNetworkOptimizerMetrics() {
    this.logger.log('获取网络优化指标');
    return this.networkOptimizerService.getMetrics();
  }

  @Get('network-optimizer/statistics')
  @ApiOperation({ summary: '获取网络优化统计信息' })
  @ApiResponse({ status: 200, description: '网络优化统计信息' })
  async getNetworkOptimizerStatistics() {
    this.logger.log('获取网络优化统计信息');
    return this.networkOptimizerService.getStatistics();
  }

  // ==================== 性能监控接口 (新增) ====================

  @Post('performance-monitor/collect/:nodeId')
  @ApiOperation({ summary: '收集节点性能指标' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '指标收集成功' })
  async collectNodeMetrics(@Param('nodeId') nodeId: string) {
    this.logger.log(`收集节点性能指标: ${nodeId}`);
    await this.performanceMonitorService.collectNodeMetrics(nodeId);
    return { success: true, message: '指标收集成功' };
  }

  @Post('performance-monitor/metric/:nodeId')
  @ApiOperation({ summary: '添加自定义指标' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '指标添加成功' })
  async addCustomMetric(
    @Param('nodeId') nodeId: string,
    @Body() metric: any
  ) {
    this.logger.log(`添加自定义指标: ${nodeId}`);
    await this.performanceMonitorService.addCustomMetric(nodeId, metric);
    return { success: true, message: '指标添加成功' };
  }

  @Post('performance-monitor/alert-rule')
  @ApiOperation({ summary: '创建告警规则' })
  @ApiResponse({ status: 200, description: '告警规则创建成功' })
  async createAlertRule(@Body() rule: AlertRule) {
    this.logger.log(`创建告警规则: ${rule.name}`);
    await this.performanceMonitorService.createAlertRule(rule);
    return { success: true, message: '告警规则创建成功' };
  }

  @Get('performance-monitor/node/:nodeId')
  @ApiOperation({ summary: '获取节点性能数据' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '节点性能数据' })
  async getNodePerformance(@Param('nodeId') nodeId: string) {
    this.logger.log(`获取节点性能数据: ${nodeId}`);
    const performance = this.performanceMonitorService.getNodePerformance(nodeId);
    return { success: true, performance };
  }

  @Get('performance-monitor/nodes')
  @ApiOperation({ summary: '获取所有节点性能数据' })
  @ApiResponse({ status: 200, description: '所有节点性能数据' })
  async getAllNodePerformance() {
    this.logger.log('获取所有节点性能数据');
    const performances = this.performanceMonitorService.getAllNodePerformance();
    return { success: true, performances };
  }

  @Get('performance-monitor/alerts')
  @ApiOperation({ summary: '获取活跃告警' })
  @ApiResponse({ status: 200, description: '活跃告警列表' })
  async getActiveAlerts() {
    this.logger.log('获取活跃告警');
    const alerts = this.performanceMonitorService.getActiveAlerts();
    return { success: true, alerts };
  }

  @Get('performance-monitor/trends')
  @ApiOperation({ summary: '分析性能趋势' })
  @ApiResponse({ status: 200, description: '性能趋势分析' })
  async analyzePerformanceTrends() {
    this.logger.log('分析性能趋势');
    const trends = await this.performanceMonitorService.analyzePerformanceTrends();
    return { success: true, trends };
  }

  @Get('performance-monitor/statistics')
  @ApiOperation({ summary: '获取性能监控统计信息' })
  @ApiResponse({ status: 200, description: '性能监控统计信息' })
  async getPerformanceMonitorStatistics() {
    this.logger.log('获取性能监控统计信息');
    return this.performanceMonitorService.getStatistics();
  }
}
