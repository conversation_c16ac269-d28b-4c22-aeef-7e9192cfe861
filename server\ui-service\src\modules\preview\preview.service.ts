import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import {
  PreviewSession,
  PreviewConfig,
  PreviewType,
  PreviewStatus,
  DeviceType,
  PreviewUpdateEvent,
  PreviewSnapshot,
  PreviewStats
} from './preview.interface';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private readonly sessions = new Map<string, PreviewSession>();
  private readonly snapshots = new Map<string, PreviewSnapshot>();

  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 创建预览会话
   */
  async createPreviewSession(config: PreviewConfig, userId: string): Promise<PreviewSession> {
    const sessionId = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后过期

    const session: PreviewSession = {
      id: sessionId,
      userId,
      config,
      status: PreviewStatus.GENERATING,
      url: `/api/v1/preview/${sessionId}`,
      createdAt: now,
      updatedAt: now,
      expiresAt,
      metadata: {}
    };

    // 存储会话
    this.sessions.set(sessionId, session);
    await this.cacheSession(session);

    // 异步生成预览
    this.generatePreview(session).catch(error => {
      this.logger.error(`生成预览失败: ${error.message}`, error.stack);
      this.updateSessionStatus(sessionId, PreviewStatus.ERROR);
    });

    this.logger.log(`创建预览会话: ${sessionId} for user: ${userId}`);
    return session;
  }

  /**
   * 获取预览会话
   */
  async getPreviewSession(sessionId: string): Promise<PreviewSession> {
    // 先从内存获取
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      // 从缓存获取
      session = await this.getCachedSession(sessionId);
      if (session) {
        this.sessions.set(sessionId, session);
      }
    }

    if (!session) {
      throw new NotFoundException('预览会话不存在');
    }

    // 检查是否过期
    if (session.expiresAt < new Date()) {
      await this.deletePreviewSession(sessionId);
      throw new NotFoundException('预览会话已过期');
    }

    return session;
  }

  /**
   * 更新预览配置
   */
  async updatePreviewConfig(sessionId: string, config: Partial<PreviewConfig>): Promise<PreviewSession> {
    const session = await this.getPreviewSession(sessionId);
    
    // 更新配置
    Object.assign(session.config, config);
    session.updatedAt = new Date();
    session.status = PreviewStatus.GENERATING;

    // 更新存储
    this.sessions.set(sessionId, session);
    await this.cacheSession(session);

    // 触发更新事件
    this.emitUpdateEvent(sessionId, 'config', config);

    // 重新生成预览
    this.generatePreview(session).catch(error => {
      this.logger.error(`重新生成预览失败: ${error.message}`, error.stack);
      this.updateSessionStatus(sessionId, PreviewStatus.ERROR);
    });

    return session;
  }

  /**
   * 删除预览会话
   */
  async deletePreviewSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
    await this.redis.del(`preview_session:${sessionId}`);
    
    // 删除相关快照
    const snapshots = Array.from(this.snapshots.values()).filter(s => s.sessionId === sessionId);
    for (const snapshot of snapshots) {
      this.snapshots.delete(snapshot.id);
      await this.redis.del(`preview_snapshot:${snapshot.id}`);
    }

    this.logger.log(`删除预览会话: ${sessionId}`);
  }

  /**
   * 创建预览快照
   */
  async createSnapshot(sessionId: string): Promise<PreviewSnapshot> {
    const session = await this.getPreviewSession(sessionId);
    
    if (session.status !== PreviewStatus.READY) {
      throw new BadRequestException('预览未就绪，无法创建快照');
    }

    const snapshotId = uuidv4();
    const snapshot: PreviewSnapshot = {
      id: snapshotId,
      sessionId,
      imageUrl: `/api/v1/preview/${sessionId}/snapshot/${snapshotId}`,
      config: { ...session.config },
      createdAt: new Date(),
      metadata: {}
    };

    // 存储快照
    this.snapshots.set(snapshotId, snapshot);
    await this.redis.setex(`preview_snapshot:${snapshotId}`, 86400, JSON.stringify(snapshot));

    // 生成快照图片
    await this.generateSnapshotImage(snapshot);

    this.logger.log(`创建预览快照: ${snapshotId} for session: ${sessionId}`);
    return snapshot;
  }

  /**
   * 获取用户的预览会话列表
   */
  async getUserPreviewSessions(userId: string): Promise<PreviewSession[]> {
    const userSessions = Array.from(this.sessions.values())
      .filter(session => session.userId === userId)
      .filter(session => session.expiresAt > new Date())
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

    return userSessions;
  }

  /**
   * 获取预览统计信息
   */
  async getPreviewStats(): Promise<PreviewStats> {
    const activeSessions = Array.from(this.sessions.values())
      .filter(session => session.expiresAt > new Date());

    const deviceStats = new Map<DeviceType, number>();
    const viewportStats = new Map<string, number>();

    activeSessions.forEach(session => {
      // 统计设备类型
      const device = session.config.device;
      deviceStats.set(device, (deviceStats.get(device) || 0) + 1);

      // 统计视口大小
      const viewport = `${session.config.viewport.width}x${session.config.viewport.height}`;
      viewportStats.set(viewport, (viewportStats.get(viewport) || 0) + 1);
    });

    return {
      totalSessions: this.sessions.size,
      activeSessions: activeSessions.length,
      totalSnapshots: this.snapshots.size,
      popularDevices: Array.from(deviceStats.entries()).map(([device, count]) => ({ device, count })),
      popularViewports: Array.from(viewportStats.entries()).map(([viewport, count]) => ({ viewport, count })),
      averageSessionDuration: 0, // 需要实现会话时长统计
      lastUpdated: new Date()
    };
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    const expiredSessions = Array.from(this.sessions.values())
      .filter(session => session.expiresAt < now);

    for (const session of expiredSessions) {
      await this.deletePreviewSession(session.id);
    }

    this.logger.log(`清理了 ${expiredSessions.length} 个过期预览会话`);
  }

  // 私有方法

  /**
   * 生成预览
   */
  private async generatePreview(session: PreviewSession): Promise<void> {
    try {
      this.logger.log(`开始生成预览: ${session.id}`);

      // 根据预览类型生成不同的预览内容
      switch (session.config.type) {
        case PreviewType.COMPONENT:
          await this.generateComponentPreview(session);
          break;
        case PreviewType.TEMPLATE:
          await this.generateTemplatePreview(session);
          break;
        case PreviewType.THEME:
          await this.generateThemePreview(session);
          break;
        case PreviewType.PAGE:
          await this.generatePagePreview(session);
          break;
        default:
          throw new Error(`不支持的预览类型: ${session.config.type}`);
      }

      // 更新状态为就绪
      await this.updateSessionStatus(session.id, PreviewStatus.READY);
      
      this.logger.log(`预览生成完成: ${session.id}`);
    } catch (error) {
      this.logger.error(`预览生成失败: ${session.id} - ${error.message}`, error.stack);
      await this.updateSessionStatus(session.id, PreviewStatus.ERROR);
      throw error;
    }
  }

  /**
   * 生成组件预览
   */
  private async generateComponentPreview(session: PreviewSession): Promise<void> {
    // 实现组件预览生成逻辑
    // 这里应该调用组件服务获取组件数据，然后渲染预览
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟异步处理
  }

  /**
   * 生成模板预览
   */
  private async generateTemplatePreview(session: PreviewSession): Promise<void> {
    // 实现模板预览生成逻辑
    await new Promise(resolve => setTimeout(resolve, 1500)); // 模拟异步处理
  }

  /**
   * 生成主题预览
   */
  private async generateThemePreview(session: PreviewSession): Promise<void> {
    // 实现主题预览生成逻辑
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟异步处理
  }

  /**
   * 生成页面预览
   */
  private async generatePagePreview(session: PreviewSession): Promise<void> {
    // 实现页面预览生成逻辑
    await new Promise(resolve => setTimeout(resolve, 2500)); // 模拟异步处理
  }

  /**
   * 生成快照图片
   */
  private async generateSnapshotImage(snapshot: PreviewSnapshot): Promise<void> {
    // 实现快照图片生成逻辑
    // 这里应该使用无头浏览器截图
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟异步处理
  }

  /**
   * 更新会话状态
   */
  private async updateSessionStatus(sessionId: string, status: PreviewStatus): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.status = status;
      session.updatedAt = new Date();
      await this.cacheSession(session);
      this.emitUpdateEvent(sessionId, 'status', { status });
    }
  }

  /**
   * 缓存会话
   */
  private async cacheSession(session: PreviewSession): Promise<void> {
    const ttl = Math.floor((session.expiresAt.getTime() - Date.now()) / 1000);
    await this.redis.setex(`preview_session:${session.id}`, ttl, JSON.stringify(session));
  }

  /**
   * 获取缓存的会话
   */
  private async getCachedSession(sessionId: string): Promise<PreviewSession | null> {
    const cached = await this.redis.get(`preview_session:${sessionId}`);
    return cached ? JSON.parse(cached) : null;
  }

  /**
   * 触发更新事件
   */
  private emitUpdateEvent(sessionId: string, type: 'config' | 'content' | 'status', data: any): void {
    const event: PreviewUpdateEvent = {
      sessionId,
      type,
      data,
      timestamp: new Date()
    };

    this.eventEmitter.emit('preview.updated', event);
  }
}
